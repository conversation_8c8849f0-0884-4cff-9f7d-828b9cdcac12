"""
Authentication service with Keycloak integration
Handles user authentication, authorization, and JWT token management
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import jwt
import httpx
from passlib.context import CryptContext

from ..config import Settings, get_settings
from ..utils.encryption import VaultManager

logger = logging.getLogger(__name__)


class AuthService:
    """Authentication and authorization service"""
    
    def __init__(self, vault_manager: VaultManager):
        self.vault_manager = vault_manager
        self.settings = get_settings()
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.keycloak_client = None
        
    async def initialize(self):
        """Initialize authentication service"""
        # Initialize Keycloak client if configured
        if self.settings.keycloak_url:
            self.keycloak_client = KeycloakClient(self.settings)
            await self.keycloak_client.initialize()
        
        logger.info("Authentication service initialized")
    
    async def authenticate(self, username: str, password: str) -> str:
        """Authenticate user and return JWT token"""
        try:
            # Try Keycloak authentication first
            if self.keycloak_client:
                user_info = await self.keycloak_client.authenticate(username, password)
                if user_info:
                    return await self._create_jwt_token(user_info)
            
            # Fallback to local authentication
            user_info = await self._authenticate_local(username, password)
            if user_info:
                return await self._create_jwt_token(user_info)
            
            raise ValueError("Invalid credentials")
            
        except Exception as e:
            logger.error(f"Authentication failed for user {username}: {e}")
            raise
    
    async def _authenticate_local(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Local authentication (fallback)"""
        # This is a simplified implementation
        # In production, you would check against a user database
        
        # For demo purposes, accept admin/admin
        if username == "admin" and password == "admin":
            return {
                "username": username,
                "email": "<EMAIL>",
                "roles": ["admin", "accountant"],
                "permissions": ["read", "write", "admin"]
            }
        
        return None
    
    async def _create_jwt_token(self, user_info: Dict[str, Any]) -> str:
        """Create JWT token for authenticated user"""
        payload = {
            "sub": user_info["username"],
            "email": user_info.get("email"),
            "roles": user_info.get("roles", []),
            "permissions": user_info.get("permissions", []),
            "iat": datetime.now(timezone.utc),
            "exp": datetime.now(timezone.utc) + timedelta(hours=self.settings.jwt_expiration_hours)
        }
        
        token = jwt.encode(
            payload,
            self.settings.jwt_secret_key,
            algorithm=self.settings.jwt_algorithm
        )
        
        return token
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token and return user info"""
        try:
            payload = jwt.decode(
                token,
                self.settings.jwt_secret_key,
                algorithms=[self.settings.jwt_algorithm]
            )
            return payload
        except jwt.ExpiredSignatureError:
            raise ValueError("Token has expired")
        except jwt.InvalidTokenError:
            raise ValueError("Invalid token")
    
    async def refresh_token(self, token: str) -> str:
        """Refresh JWT token"""
        try:
            # Verify current token (allowing expired tokens for refresh)
            payload = jwt.decode(
                token,
                self.settings.jwt_secret_key,
                algorithms=[self.settings.jwt_algorithm],
                options={"verify_exp": False}
            )
            
            # Create new token with extended expiration
            new_payload = {
                **payload,
                "iat": datetime.now(timezone.utc),
                "exp": datetime.now(timezone.utc) + timedelta(hours=self.settings.jwt_expiration_hours)
            }
            
            new_token = jwt.encode(
                new_payload,
                self.settings.jwt_secret_key,
                algorithm=self.settings.jwt_algorithm
            )
            
            return new_token
            
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            raise ValueError("Token refresh failed")
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    async def check_permission(self, token: str, required_permission: str) -> bool:
        """Check if user has required permission"""
        try:
            payload = await self.verify_token(token)
            permissions = payload.get("permissions", [])
            return required_permission in permissions or "admin" in permissions
        except Exception:
            return False
    
    async def check_role(self, token: str, required_role: str) -> bool:
        """Check if user has required role"""
        try:
            payload = await self.verify_token(token)
            roles = payload.get("roles", [])
            return required_role in roles or "admin" in roles
        except Exception:
            return False


class KeycloakClient:
    """Keycloak integration client"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.base_url = f"{settings.keycloak_url}/auth/realms/{settings.keycloak_realm}"
        self.client_id = settings.keycloak_client_id
        self.client_secret = settings.keycloak_client_secret
        self.http_client = None
        
    async def initialize(self):
        """Initialize Keycloak client"""
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # Test connection
        try:
            response = await self.http_client.get(f"{self.base_url}/.well-known/openid_configuration")
            response.raise_for_status()
            logger.info("Keycloak client initialized successfully")
        except Exception as e:
            logger.warning(f"Keycloak connection test failed: {e}")
    
    async def authenticate(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user with Keycloak"""
        if not self.http_client:
            return None
        
        try:
            # Get access token
            token_url = f"{self.base_url}/protocol/openid-connect/token"
            token_data = {
                "grant_type": "password",
                "client_id": self.client_id,
                "username": username,
                "password": password
            }
            
            if self.client_secret:
                token_data["client_secret"] = self.client_secret
            
            response = await self.http_client.post(token_url, data=token_data)
            response.raise_for_status()
            
            token_info = response.json()
            access_token = token_info["access_token"]
            
            # Get user info
            userinfo_url = f"{self.base_url}/protocol/openid-connect/userinfo"
            headers = {"Authorization": f"Bearer {access_token}"}
            
            response = await self.http_client.get(userinfo_url, headers=headers)
            response.raise_for_status()
            
            user_info = response.json()
            
            # Extract roles and permissions from token
            roles = await self._extract_roles_from_token(access_token)
            
            return {
                "username": user_info.get("preferred_username", username),
                "email": user_info.get("email"),
                "first_name": user_info.get("given_name"),
                "last_name": user_info.get("family_name"),
                "roles": roles,
                "permissions": await self._map_roles_to_permissions(roles)
            }
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                logger.warning(f"Keycloak authentication failed for user {username}")
                return None
            else:
                logger.error(f"Keycloak error: {e}")
                raise
        except Exception as e:
            logger.error(f"Keycloak authentication error: {e}")
            return None
    
    async def _extract_roles_from_token(self, access_token: str) -> list:
        """Extract roles from Keycloak access token"""
        try:
            # Decode token without verification (we trust Keycloak)
            payload = jwt.decode(access_token, options={"verify_signature": False})
            
            # Extract realm roles
            realm_access = payload.get("realm_access", {})
            roles = realm_access.get("roles", [])
            
            # Extract client roles
            resource_access = payload.get("resource_access", {})
            client_access = resource_access.get(self.client_id, {})
            client_roles = client_access.get("roles", [])
            
            return list(set(roles + client_roles))
            
        except Exception as e:
            logger.error(f"Failed to extract roles from token: {e}")
            return []
    
    async def _map_roles_to_permissions(self, roles: list) -> list:
        """Map Keycloak roles to application permissions"""
        permission_mapping = {
            "admin": ["read", "write", "admin", "delete"],
            "accountant": ["read", "write"],
            "viewer": ["read"],
            "payroll_manager": ["read", "write", "payroll"],
            "financial_analyst": ["read", "reports"]
        }
        
        permissions = set()
        for role in roles:
            if role in permission_mapping:
                permissions.update(permission_mapping[role])
        
        return list(permissions)
    
    async def close(self):
        """Close HTTP client"""
        if self.http_client:
            await self.http_client.aclose()
