"""
Payroll models for payroll processing
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..utils.db import Base


class PayrollRun(Base):
    """Payroll run header"""
    
    __tablename__ = "payroll_runs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Period information
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    pay_date = Column(DateTime(timezone=True), nullable=False)
    
    # Status
    status = Column(String(20), default="draft")  # draft, processing, completed, cancelled
    
    # Totals (encrypted)
    total_gross_pay = Column(Text)
    total_net_pay = Column(Text)
    total_taxes = Column(Text)
    total_deductions = Column(Text)
    
    # Relationships
    items = relationship("PayrollItem", back_populates="payroll_run", cascade="all, delete-orphan")
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100))
    
    def __repr__(self):
        return f"<PayrollRun(id={self.id}, period='{self.period_start}' to '{self.period_end}')>"


class PayrollItem(Base):
    """Individual employee payroll item"""
    
    __tablename__ = "payroll_items"
    
    id = Column(Integer, primary_key=True, index=True)
    payroll_run_id = Column(Integer, ForeignKey("payroll_runs.id"), nullable=False)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    
    # Pay amounts (encrypted)
    gross_pay = Column(Text, nullable=False)
    net_pay = Column(Text, nullable=False)
    
    # Tax withholdings (encrypted)
    federal_tax = Column(Text)
    state_tax = Column(Text)
    social_security = Column(Text)
    medicare = Column(Text)
    total_taxes = Column(Text)
    
    # Other deductions (encrypted)
    health_insurance = Column(Text)
    retirement_401k = Column(Text)
    other_deductions = Column(Text)
    total_deductions = Column(Text)
    
    # Hours and overtime
    regular_hours = Column(Float, default=0.0)
    overtime_hours = Column(Float, default=0.0)
    overtime_pay = Column(Text)  # Encrypted overtime pay
    
    # Relationships
    payroll_run = relationship("PayrollRun", back_populates="items")
    employee = relationship("Employee")
    
    def __repr__(self):
        return f"<PayrollItem(id={self.id}, employee_id={self.employee_id}, payroll_run_id={self.payroll_run_id})>"
