"""
Database utilities and SQLAlchemy setup for Enterprise Accounting System
Provides database connection, session management, and initialization
"""

import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from ..config import Settings, get_database_config

logger = logging.getLogger(__name__)

# Database base class
Base = declarative_base()

# Global database components
_async_engine = None
_sync_engine = None
_async_session_factory = None
_sync_session_factory = None


async def init_database(settings: Settings):
    """Initialize database engines and session factories"""
    global _async_engine, _sync_engine, _async_session_factory, _sync_session_factory
    
    try:
        db_config = get_database_config()
        
        # Create async engine
        async_url = db_config["url"].replace("postgresql://", "postgresql+asyncpg://")
        _async_engine = create_async_engine(
            async_url,
            echo=db_config["echo"],
            pool_size=db_config["pool_size"],
            max_overflow=db_config["max_overflow"],
            pool_pre_ping=db_config["pool_pre_ping"],
            pool_recycle=db_config["pool_recycle"],
        )
        
        # Create sync engine for migrations and admin tasks
        _sync_engine = create_engine(
            db_config["url"],
            echo=db_config["echo"],
            pool_size=db_config["pool_size"],
            max_overflow=db_config["max_overflow"],
            pool_pre_ping=db_config["pool_pre_ping"],
            pool_recycle=db_config["pool_recycle"],
        )
        
        # Create session factories
        _async_session_factory = async_sessionmaker(
            _async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        _sync_session_factory = sessionmaker(
            _sync_engine,
            expire_on_commit=False
        )
        
        # Test connections
        await test_database_connection()
        
        # Create tables if they don't exist
        await create_tables()
        
        # Enable pgcrypto extension for PostgreSQL
        await enable_pgcrypto()
        
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


async def test_database_connection():
    """Test database connectivity"""
    try:
        async with _async_engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            assert result.scalar() == 1
        logger.info("Database connection test successful")
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        raise


async def create_tables():
    """Create database tables"""
    try:
        # Import all models to ensure they're registered
        from ..models import (
            account, journal, transaction, customer, vendor,
            employee, payroll, tax, report
        )
        
        async with _async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created/verified")
        
    except Exception as e:
        logger.error(f"Table creation failed: {e}")
        raise


async def enable_pgcrypto():
    """Enable pgcrypto extension for PostgreSQL"""
    try:
        async with _async_engine.begin() as conn:
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS pgcrypto"))
        logger.info("pgcrypto extension enabled")
    except Exception as e:
        logger.warning(f"Could not enable pgcrypto extension: {e}")


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session"""
    if _async_session_factory is None:
        raise RuntimeError("Database not initialized")
    
    async with _async_session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


@asynccontextmanager
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Context manager for async database sessions"""
    if _async_session_factory is None:
        raise RuntimeError("Database not initialized")
    
    async with _async_session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise


def get_sync_session() -> Session:
    """Get synchronous database session"""
    if _sync_session_factory is None:
        raise RuntimeError("Database not initialized")
    
    return _sync_session_factory()


@asynccontextmanager
async def get_transaction():
    """Context manager for database transactions"""
    async with get_async_session() as session:
        async with session.begin():
            yield session


class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    async def health_check() -> dict:
        """Check database health"""
        try:
            async with get_async_session() as session:
                result = await session.execute(text("SELECT 1"))
                assert result.scalar() == 1
            
            return {
                "status": "healthy",
                "database": "connected",
                "engine": str(_async_engine.url) if _async_engine else "not initialized"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "database": "disconnected"
            }
    
    @staticmethod
    async def get_connection_info() -> dict:
        """Get database connection information"""
        if _async_engine is None:
            return {"status": "not initialized"}
        
        pool = _async_engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    
    @staticmethod
    async def execute_raw_sql(sql: str, params: dict = None) -> list:
        """Execute raw SQL query"""
        async with get_async_session() as session:
            result = await session.execute(text(sql), params or {})
            return result.fetchall()
    
    @staticmethod
    async def backup_database(backup_path: str):
        """Create database backup (PostgreSQL specific)"""
        import subprocess
        import os
        
        try:
            db_url = str(_sync_engine.url)
            # Extract connection details from URL
            # This is a simplified version - in production, use proper URL parsing
            
            cmd = [
                "pg_dump",
                "--no-password",
                "--format=custom",
                "--file", backup_path,
                db_url
            ]
            
            subprocess.run(cmd, check=True)
            logger.info(f"Database backup created: {backup_path}")
            
        except Exception as e:
            logger.error(f"Database backup failed: {e}")
            raise
    
    @staticmethod
    async def restore_database(backup_path: str):
        """Restore database from backup (PostgreSQL specific)"""
        import subprocess
        
        try:
            db_url = str(_sync_engine.url)
            
            cmd = [
                "pg_restore",
                "--no-password",
                "--clean",
                "--if-exists",
                "--dbname", db_url,
                backup_path
            ]
            
            subprocess.run(cmd, check=True)
            logger.info(f"Database restored from: {backup_path}")
            
        except Exception as e:
            logger.error(f"Database restore failed: {e}")
            raise


# Migration utilities
class MigrationManager:
    """Database migration management"""
    
    @staticmethod
    async def create_migration_table():
        """Create migration tracking table"""
        sql = """
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version VARCHAR(255) PRIMARY KEY,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        async with get_async_session() as session:
            await session.execute(text(sql))
    
    @staticmethod
    async def get_applied_migrations() -> list:
        """Get list of applied migrations"""
        await MigrationManager.create_migration_table()
        
        sql = "SELECT version FROM schema_migrations ORDER BY applied_at"
        async with get_async_session() as session:
            result = await session.execute(text(sql))
            return [row[0] for row in result.fetchall()]
    
    @staticmethod
    async def mark_migration_applied(version: str):
        """Mark migration as applied"""
        sql = "INSERT INTO schema_migrations (version) VALUES (:version)"
        async with get_async_session() as session:
            await session.execute(text(sql), {"version": version})


# Database utilities for testing
class TestDatabaseManager:
    """Database utilities for testing"""
    
    @staticmethod
    async def create_test_database():
        """Create test database"""
        from ..models import Base
        
        # Use in-memory SQLite for tests
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False}
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        return engine
    
    @staticmethod
    async def cleanup_test_database(engine):
        """Cleanup test database"""
        await engine.dispose()
