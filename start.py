#!/usr/bin/env python3
"""
Enterprise Accounting System Startup Script
Automated setup and launch script for development and production environments
"""

import os
import sys
import subprocess
import time
import argparse
import json
from pathlib import Path
import platform

def print_banner():
    """Print application banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        Enterprise Accounting & Financial Management          ║
    ║                                                              ║
    ║        High-Performance System with Python + C++ + Electron ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_prerequisites():
    """Check if all prerequisites are installed"""
    print("🔍 Checking prerequisites...")
    
    requirements = {
        'python': {'cmd': ['python', '--version'], 'min_version': '3.11'},
        'node': {'cmd': ['node', '--version'], 'min_version': '16.0'},
        'cmake': {'cmd': ['cmake', '--version'], 'min_version': '3.12'},
        'docker': {'cmd': ['docker', '--version'], 'min_version': '20.0'},
        'docker-compose': {'cmd': ['docker-compose', '--version'], 'min_version': '2.0'}
    }
    
    missing = []
    
    for name, req in requirements.items():
        try:
            result = subprocess.run(req['cmd'], capture_output=True, text=True, check=True)
            print(f"  ✅ {name}: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"  ❌ {name}: Not found or version too old")
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing prerequisites: {', '.join(missing)}")
        print("Please install the missing components and try again.")
        return False
    
    print("✅ All prerequisites satisfied!")
    return True

def setup_environment():
    """Set up the development environment"""
    print("\n🔧 Setting up environment...")
    
    # Create .env file if it doesn't exist
    if not Path('.env').exists():
        print("  📝 Creating .env file from template...")
        subprocess.run(['cp', '.env.example', '.env'], check=True)
        print("  ⚠️  Please edit .env file with your configuration before continuing")
        return False
    
    # Create necessary directories
    directories = ['uploads', 'logs', 'data', 'backups']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"  📁 Created directory: {directory}")
    
    return True

def install_python_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
        print("✅ Python dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Python dependencies: {e}")
        return False

def build_cpp_modules():
    """Build C++ modules"""
    print("\n🔨 Building C++ modules...")
    
    try:
        subprocess.run([sys.executable, 'main.py', 'build-modules'], check=True)
        print("✅ C++ modules built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build C++ modules: {e}")
        print("  💡 This is optional - the system will fall back to Python implementations")
        return True  # Continue even if C++ build fails

def install_node_dependencies():
    """Install Node.js dependencies for Electron"""
    print("\n📦 Installing Node.js dependencies...")
    
    try:
        os.chdir('electron')
        subprocess.run(['npm', 'install'], check=True)
        os.chdir('..')
        print("✅ Node.js dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Node.js dependencies: {e}")
        return False

def start_infrastructure(mode='development'):
    """Start infrastructure services using Docker"""
    print(f"\n🐳 Starting infrastructure services ({mode} mode)...")
    
    try:
        if mode == 'production':
            subprocess.run(['docker-compose', '-f', 'docker-compose.yml', '-f', 'docker-compose.prod.yml', 'up', '-d'], check=True)
        else:
            subprocess.run(['docker-compose', 'up', '-d', 'postgres', 'redis', 'rabbitmq', 'vault'], check=True)
        
        print("✅ Infrastructure services started!")
        
        # Wait for services to be ready
        print("⏳ Waiting for services to be ready...")
        time.sleep(10)
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start infrastructure: {e}")
        return False

def initialize_database():
    """Initialize the database"""
    print("\n🗄️  Initializing database...")
    
    try:
        subprocess.run([sys.executable, 'main.py', 'init-db'], check=True)
        print("✅ Database initialized successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to initialize database: {e}")
        return False

def start_backend():
    """Start the Python backend"""
    print("\n🚀 Starting Python backend...")
    
    try:
        # Start backend in background
        process = subprocess.Popen([sys.executable, 'main.py', 'serve'])
        
        # Wait for backend to start
        print("⏳ Waiting for backend to start...")
        time.sleep(5)
        
        # Check if backend is running
        try:
            import requests
            response = requests.get('http://localhost:8000/health', timeout=5)
            if response.status_code == 200:
                print("✅ Backend started successfully!")
                return process
            else:
                print("❌ Backend health check failed")
                return None
        except:
            print("❌ Backend not responding")
            return None
            
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None

def start_electron():
    """Start the Electron application"""
    print("\n🖥️  Starting Electron application...")
    
    try:
        os.chdir('electron')
        subprocess.run(['npm', 'start'], check=True)
        os.chdir('..')
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Electron application: {e}")
        return False

def main():
    """Main startup function"""
    parser = argparse.ArgumentParser(description='Enterprise Accounting System Startup Script')
    parser.add_argument('--mode', choices=['development', 'production'], default='development',
                       help='Deployment mode (default: development)')
    parser.add_argument('--skip-deps', action='store_true',
                       help='Skip dependency installation')
    parser.add_argument('--skip-build', action='store_true',
                       help='Skip C++ module building')
    parser.add_argument('--skip-infrastructure', action='store_true',
                       help='Skip infrastructure startup (assume already running)')
    parser.add_argument('--backend-only', action='store_true',
                       help='Start backend only (no Electron UI)')
    
    args = parser.parse_args()
    
    print_banner()
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Set up environment
    if not setup_environment():
        print("\n⚠️  Please configure your .env file and run the script again.")
        sys.exit(1)
    
    # Install dependencies
    if not args.skip_deps:
        if not install_python_dependencies():
            sys.exit(1)
        
        if not args.backend_only and not install_node_dependencies():
            sys.exit(1)
    
    # Build C++ modules
    if not args.skip_build:
        build_cpp_modules()
    
    # Start infrastructure
    if not args.skip_infrastructure:
        if not start_infrastructure(args.mode):
            sys.exit(1)
    
    # Initialize database
    if not initialize_database():
        sys.exit(1)
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        sys.exit(1)
    
    try:
        if args.backend_only:
            print("\n🎉 Backend started successfully!")
            print("📍 API Documentation: http://localhost:8000/docs")
            print("🔍 Health Check: http://localhost:8000/health")
            print("\nPress Ctrl+C to stop the backend...")
            backend_process.wait()
        else:
            # Start Electron application
            start_electron()
    
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        if backend_process:
            backend_process.terminate()
            backend_process.wait()
    
    print("👋 Goodbye!")

if __name__ == '__main__':
    main()
