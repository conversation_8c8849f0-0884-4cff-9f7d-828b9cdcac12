/**
 * Tax Calculator Header
 * High-performance tax calculations for payroll and transactions
 */

#ifndef TAX_CALCULATOR_H
#define TAX_CALCULATOR_H

#include <string>
#include <map>
#include <vector>
#include <memory>

class TaxCalculator {
public:
    TaxCalculator();
    ~TaxCalculator();
    
    /**
     * Calculate complete tax withholding
     */
    std::map<std::string, double> calculate_withholding(
        double gross_pay,
        const std::string& filing_status,
        int allowances,
        const std::string& state
    );
    
    /**
     * Calculate federal income tax
     */
    double calculate_federal_income_tax(
        double gross_pay,
        const std::string& filing_status,
        int allowances,
        double additional_withholding = 0.0
    );
    
    /**
     * Calculate state income tax
     */
    double calculate_state_income_tax(
        double gross_pay,
        const std::string& state,
        const std::string& filing_status
    );
    
    /**
     * Calculate Social Security tax
     */
    double calculate_social_security_tax(
        double gross_pay,
        double ytd_gross = 0.0,
        double wage_base = 160200.0
    );
    
    /**
     * Calculate Medicare tax
     */
    double calculate_medicare_tax(
        double gross_pay,
        double ytd_gross = 0.0,
        const std::string& filing_status = "single"
    );
    
    /**
     * Calculate FUTA tax (employer portion)
     */
    double calculate_futa_tax(
        double gross_pay,
        double ytd_gross = 0.0,
        double wage_base = 7000.0,
        double rate = 0.006
    );
    
    /**
     * Calculate SUTA tax (employer portion)
     */
    double calculate_suta_tax(
        double gross_pay,
        const std::string& state,
        double ytd_gross = 0.0,
        double experience_rate = 0.0
    );
    
    /**
     * Calculate sales tax
     */
    double calculate_sales_tax(
        double amount,
        const std::string& state,
        const std::string& county = "",
        const std::string& city = ""
    );
    
    /**
     * Calculate property tax
     */
    double calculate_property_tax(
        double assessed_value,
        double mill_rate,
        double exemption_amount = 0.0
    );
    
    /**
     * Calculate estimated quarterly tax
     */
    double calculate_estimated_quarterly_tax(
        double quarterly_income,
        const std::string& filing_status,
        double prior_year_tax = 0.0
    );
    
    /**
     * Calculate tax penalty
     */
    double calculate_tax_penalty(
        double tax_owed,
        double tax_paid,
        int days_late,
        double penalty_rate = 0.05
    );
    
    /**
     * Calculate tax interest
     */
    double calculate_tax_interest(
        double tax_owed,
        int days_late,
        double annual_interest_rate = 0.03
    );
    
    /**
     * Get tax brackets for filing status
     */
    std::vector<std::map<std::string, double>> get_tax_brackets(
        const std::string& filing_status,
        int tax_year = 2023
    );
    
    /**
     * Calculate effective tax rate
     */
    double calculate_effective_tax_rate(
        double total_tax,
        double total_income
    );
    
    /**
     * Calculate marginal tax rate
     */
    double calculate_marginal_tax_rate(
        double income,
        const std::string& filing_status
    );

private:
    /**
     * Internal calculation helpers
     */
    double calculate_tax_from_brackets(
        double income,
        const std::vector<std::map<std::string, double>>& brackets
    );
    
    double get_state_tax_rate(const std::string& state);
    double get_sales_tax_rate(const std::string& state, const std::string& county, const std::string& city);
    double get_suta_rate(const std::string& state, double experience_rate);
    
    /**
     * Tax table data
     */
    void initialize_tax_tables();
    void initialize_state_rates();
    void initialize_sales_tax_rates();
    
    // Tax tables and rates
    std::map<std::string, std::vector<std::map<std::string, double>>> federal_tax_brackets_;
    std::map<std::string, double> state_tax_rates_;
    std::map<std::string, double> sales_tax_rates_;
    std::map<std::string, double> suta_rates_;
    
    // Configuration
    int current_tax_year_;
    double social_security_rate_;
    double medicare_rate_;
    double additional_medicare_rate_;
    double futa_rate_;
};

#endif // TAX_CALCULATOR_H
