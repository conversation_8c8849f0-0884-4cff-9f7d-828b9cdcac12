"""
Account model for chart of accounts
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..utils.db import Base


class Account(Base):
    """Chart of accounts model"""
    
    __tablename__ = "accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(20), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Account classification
    account_type = Column(String(50), nullable=False)  # asset, liability, equity, revenue, expense
    account_subtype = Column(String(50))  # current_asset, fixed_asset, etc.
    
    # Hierarchy
    parent_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)
    parent = relationship("Account", remote_side=[id], backref="children")
    
    # Account properties
    is_active = Column(Boolean, default=True, nullable=False)
    is_current = Column(Boolean, default=False, nullable=False)  # For current vs non-current classification
    is_cash = Column(Boolean, default=False, nullable=False)  # Cash and cash equivalents
    is_operating = Column(Boolean, default=True, nullable=False)  # Operating vs non-operating
    
    # Balance information (encrypted)
    current_balance = Column(Text)  # Encrypted balance
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100))
    
    def __repr__(self):
        return f"<Account(code='{self.code}', name='{self.name}', type='{self.account_type}')>"
