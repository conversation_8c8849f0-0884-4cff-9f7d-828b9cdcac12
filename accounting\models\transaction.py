"""
Transaction model for financial transactions
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.sql import func

from ..utils.db import Base


class Transaction(Base):
    """Financial transaction model"""
    
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    transaction_id = Column(String(50), unique=True, nullable=False, index=True)
    
    # Transaction details
    description = Column(String(500), nullable=False)
    transaction_type = Column(String(50), nullable=False)  # payment, receipt, transfer, etc.
    
    # Amounts (encrypted)
    amount = Column(Text, nullable=False)
    currency = Column(String(3), default="USD")
    
    # References
    reference_number = Column(String(100))
    external_reference = Column(String(100))
    
    # Related entities
    customer_id = Column(Integer, ForeignKey("customers.id"))
    vendor_id = Column(Integer, ForeignKey("vendors.id"))
    
    # Metadata
    transaction_date = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100))
    
    def __repr__(self):
        return f"<Transaction(id='{self.transaction_id}', type='{self.transaction_type}')>"
