"""
Report service for generating financial reports
Handles balance sheets, income statements, cash flow statements, and custom reports
"""

import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, date
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func

from ..utils.encryption import VaultManager, decrypt_field
from ..models.account import Account
from ..models.journal import JournalEntry, JournalLine

logger = logging.getLogger(__name__)


class ReportService:
    """Financial reporting service"""
    
    def __init__(self, vault_manager: VaultManager):
        self.vault_manager = vault_manager
        
    async def generate_balance_sheet(
        self, 
        as_of_date: str = None, 
        db_session: AsyncSession = None
    ) -> Dict[str, Any]:
        """Generate balance sheet report"""
        try:
            if as_of_date:
                report_date = datetime.fromisoformat(as_of_date)
            else:
                report_date = datetime.now()
            
            logger.info(f"Generating balance sheet as of {report_date}")
            
            # Get all accounts
            accounts_stmt = select(Account)
            accounts_result = await db_session.execute(accounts_stmt)
            accounts = accounts_result.scalars().all()
            
            # Initialize balance sheet structure
            balance_sheet = {
                "report_date": report_date.isoformat(),
                "assets": {
                    "current_assets": {},
                    "non_current_assets": {},
                    "total_assets": Decimal('0')
                },
                "liabilities": {
                    "current_liabilities": {},
                    "non_current_liabilities": {},
                    "total_liabilities": Decimal('0')
                },
                "equity": {
                    "equity_accounts": {},
                    "total_equity": Decimal('0')
                }
            }
            
            # Calculate balances for each account
            for account in accounts:
                balance = await self._get_account_balance(
                    account.id, report_date, db_session
                )
                
                if balance == Decimal('0'):
                    continue
                
                account_data = {
                    "name": account.name,
                    "code": account.code,
                    "balance": str(balance)
                }
                
                # Categorize accounts
                if account.account_type == "asset":
                    if account.is_current:
                        balance_sheet["assets"]["current_assets"][account.code] = account_data
                    else:
                        balance_sheet["assets"]["non_current_assets"][account.code] = account_data
                    balance_sheet["assets"]["total_assets"] += balance
                
                elif account.account_type == "liability":
                    if account.is_current:
                        balance_sheet["liabilities"]["current_liabilities"][account.code] = account_data
                    else:
                        balance_sheet["liabilities"]["non_current_liabilities"][account.code] = account_data
                    balance_sheet["liabilities"]["total_liabilities"] += balance
                
                elif account.account_type == "equity":
                    balance_sheet["equity"]["equity_accounts"][account.code] = account_data
                    balance_sheet["equity"]["total_equity"] += balance
            
            # Convert Decimal to string for JSON serialization
            balance_sheet["assets"]["total_assets"] = str(balance_sheet["assets"]["total_assets"])
            balance_sheet["liabilities"]["total_liabilities"] = str(balance_sheet["liabilities"]["total_liabilities"])
            balance_sheet["equity"]["total_equity"] = str(balance_sheet["equity"]["total_equity"])
            
            # Calculate total liabilities and equity
            total_liab_equity = (
                Decimal(balance_sheet["liabilities"]["total_liabilities"]) +
                Decimal(balance_sheet["equity"]["total_equity"])
            )
            balance_sheet["total_liabilities_and_equity"] = str(total_liab_equity)
            
            logger.info("Balance sheet generated successfully")
            return balance_sheet
            
        except Exception as e:
            logger.error(f"Failed to generate balance sheet: {e}")
            raise
    
    async def generate_income_statement(
        self, 
        start_date: str, 
        end_date: str, 
        db_session: AsyncSession
    ) -> Dict[str, Any]:
        """Generate income statement report"""
        try:
            period_start = datetime.fromisoformat(start_date)
            period_end = datetime.fromisoformat(end_date)
            
            logger.info(f"Generating income statement for {period_start} to {period_end}")
            
            # Get revenue and expense accounts
            accounts_stmt = select(Account).where(
                or_(Account.account_type == "revenue", Account.account_type == "expense")
            )
            accounts_result = await db_session.execute(accounts_stmt)
            accounts = accounts_result.scalars().all()
            
            # Initialize income statement structure
            income_statement = {
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "revenue": {
                    "revenue_accounts": {},
                    "total_revenue": Decimal('0')
                },
                "expenses": {
                    "operating_expenses": {},
                    "non_operating_expenses": {},
                    "total_expenses": Decimal('0')
                },
                "net_income": Decimal('0')
            }
            
            # Calculate balances for each account in the period
            for account in accounts:
                balance = await self._get_account_balance_for_period(
                    account.id, period_start, period_end, db_session
                )
                
                if balance == Decimal('0'):
                    continue
                
                account_data = {
                    "name": account.name,
                    "code": account.code,
                    "balance": str(balance)
                }
                
                if account.account_type == "revenue":
                    income_statement["revenue"]["revenue_accounts"][account.code] = account_data
                    income_statement["revenue"]["total_revenue"] += balance
                
                elif account.account_type == "expense":
                    if account.is_operating:
                        income_statement["expenses"]["operating_expenses"][account.code] = account_data
                    else:
                        income_statement["expenses"]["non_operating_expenses"][account.code] = account_data
                    income_statement["expenses"]["total_expenses"] += balance
            
            # Calculate net income
            net_income = (
                income_statement["revenue"]["total_revenue"] - 
                income_statement["expenses"]["total_expenses"]
            )
            
            # Convert to strings for JSON serialization
            income_statement["revenue"]["total_revenue"] = str(income_statement["revenue"]["total_revenue"])
            income_statement["expenses"]["total_expenses"] = str(income_statement["expenses"]["total_expenses"])
            income_statement["net_income"] = str(net_income)
            
            logger.info("Income statement generated successfully")
            return income_statement
            
        except Exception as e:
            logger.error(f"Failed to generate income statement: {e}")
            raise
    
    async def generate_cash_flow_statement(
        self, 
        start_date: str, 
        end_date: str, 
        db_session: AsyncSession
    ) -> Dict[str, Any]:
        """Generate cash flow statement report"""
        try:
            period_start = datetime.fromisoformat(start_date)
            period_end = datetime.fromisoformat(end_date)
            
            logger.info(f"Generating cash flow statement for {period_start} to {period_end}")
            
            # Get cash and cash equivalent accounts
            cash_accounts_stmt = select(Account).where(
                and_(Account.account_type == "asset", Account.is_cash == True)
            )
            cash_accounts_result = await db_session.execute(cash_accounts_stmt)
            cash_accounts = cash_accounts_result.scalars().all()
            
            # Initialize cash flow statement structure
            cash_flow = {
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "operating_activities": {
                    "net_income": Decimal('0'),
                    "adjustments": {},
                    "net_cash_from_operations": Decimal('0')
                },
                "investing_activities": {
                    "activities": {},
                    "net_cash_from_investing": Decimal('0')
                },
                "financing_activities": {
                    "activities": {},
                    "net_cash_from_financing": Decimal('0')
                },
                "net_change_in_cash": Decimal('0'),
                "cash_beginning": Decimal('0'),
                "cash_ending": Decimal('0')
            }
            
            # Get net income from income statement
            income_stmt = await self.generate_income_statement(start_date, end_date, db_session)
            cash_flow["operating_activities"]["net_income"] = Decimal(income_stmt["net_income"])
            
            # Calculate cash flows (simplified implementation)
            # In a real system, this would analyze journal entries by cash flow category
            
            # Calculate beginning and ending cash balances
            beginning_cash = Decimal('0')
            ending_cash = Decimal('0')
            
            for account in cash_accounts:
                # Beginning balance (as of start date)
                begin_balance = await self._get_account_balance(
                    account.id, period_start, db_session
                )
                beginning_cash += begin_balance
                
                # Ending balance (as of end date)
                end_balance = await self._get_account_balance(
                    account.id, period_end, db_session
                )
                ending_cash += end_balance
            
            # Calculate net change
            net_change = ending_cash - beginning_cash
            
            # For simplicity, attribute all change to operating activities
            cash_flow["operating_activities"]["net_cash_from_operations"] = net_change
            cash_flow["net_change_in_cash"] = net_change
            cash_flow["cash_beginning"] = str(beginning_cash)
            cash_flow["cash_ending"] = str(ending_cash)
            
            # Convert Decimals to strings
            cash_flow["operating_activities"]["net_income"] = str(cash_flow["operating_activities"]["net_income"])
            cash_flow["operating_activities"]["net_cash_from_operations"] = str(cash_flow["operating_activities"]["net_cash_from_operations"])
            cash_flow["investing_activities"]["net_cash_from_investing"] = str(cash_flow["investing_activities"]["net_cash_from_investing"])
            cash_flow["financing_activities"]["net_cash_from_financing"] = str(cash_flow["financing_activities"]["net_cash_from_financing"])
            cash_flow["net_change_in_cash"] = str(cash_flow["net_change_in_cash"])
            
            logger.info("Cash flow statement generated successfully")
            return cash_flow
            
        except Exception as e:
            logger.error(f"Failed to generate cash flow statement: {e}")
            raise
    
    async def generate_trial_balance(
        self, 
        as_of_date: str = None, 
        db_session: AsyncSession = None
    ) -> Dict[str, Any]:
        """Generate trial balance report"""
        try:
            if as_of_date:
                report_date = datetime.fromisoformat(as_of_date)
            else:
                report_date = datetime.now()
            
            logger.info(f"Generating trial balance as of {report_date}")
            
            # Get all accounts
            accounts_stmt = select(Account)
            accounts_result = await db_session.execute(accounts_stmt)
            accounts = accounts_result.scalars().all()
            
            trial_balance = {
                "report_date": report_date.isoformat(),
                "accounts": [],
                "total_debits": Decimal('0'),
                "total_credits": Decimal('0')
            }
            
            for account in accounts:
                balance = await self._get_account_balance(
                    account.id, report_date, db_session
                )
                
                if balance == Decimal('0'):
                    continue
                
                # Determine if balance is debit or credit
                is_debit_balance = account.account_type in ['asset', 'expense']
                
                account_data = {
                    "code": account.code,
                    "name": account.name,
                    "account_type": account.account_type,
                    "debit_balance": str(balance) if is_debit_balance else "0.00",
                    "credit_balance": str(balance) if not is_debit_balance else "0.00"
                }
                
                trial_balance["accounts"].append(account_data)
                
                if is_debit_balance:
                    trial_balance["total_debits"] += balance
                else:
                    trial_balance["total_credits"] += balance
            
            # Convert totals to strings
            trial_balance["total_debits"] = str(trial_balance["total_debits"])
            trial_balance["total_credits"] = str(trial_balance["total_credits"])
            
            logger.info("Trial balance generated successfully")
            return trial_balance
            
        except Exception as e:
            logger.error(f"Failed to generate trial balance: {e}")
            raise
    
    async def _get_account_balance(
        self, 
        account_id: int, 
        as_of_date: datetime, 
        db_session: AsyncSession
    ) -> Decimal:
        """Get account balance as of specific date"""
        try:
            # Get all journal lines for this account up to the date
            stmt = select(JournalLine).join(JournalEntry).where(
                and_(
                    JournalLine.account_id == account_id,
                    JournalEntry.entry_date <= as_of_date
                )
            )
            
            result = await db_session.execute(stmt)
            lines = result.scalars().all()
            
            balance = Decimal('0')
            for line in lines:
                if line.debit_amount:
                    amount = Decimal(await decrypt_field(line.debit_amount))
                    balance += amount
                elif line.credit_amount:
                    amount = Decimal(await decrypt_field(line.credit_amount))
                    balance -= amount
            
            return balance
            
        except Exception as e:
            logger.error(f"Failed to get account balance: {e}")
            return Decimal('0')
    
    async def _get_account_balance_for_period(
        self, 
        account_id: int, 
        start_date: datetime, 
        end_date: datetime, 
        db_session: AsyncSession
    ) -> Decimal:
        """Get account balance for specific period"""
        try:
            # Get journal lines for this account within the period
            stmt = select(JournalLine).join(JournalEntry).where(
                and_(
                    JournalLine.account_id == account_id,
                    JournalEntry.entry_date >= start_date,
                    JournalEntry.entry_date <= end_date
                )
            )
            
            result = await db_session.execute(stmt)
            lines = result.scalars().all()
            
            balance = Decimal('0')
            for line in lines:
                if line.debit_amount:
                    amount = Decimal(await decrypt_field(line.debit_amount))
                    balance += amount
                elif line.credit_amount:
                    amount = Decimal(await decrypt_field(line.credit_amount))
                    balance -= amount
            
            return balance
            
        except Exception as e:
            logger.error(f"Failed to get account balance for period: {e}")
            return Decimal('0')
