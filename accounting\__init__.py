"""
Enterprise Accounting & Financial Management System
Core accounting package
"""

__version__ = "1.0.0"
__author__ = "Enterprise Accounting Team"
__description__ = "High-performance accounting system with C++ engines"

# Package imports
from .config import get_settings
from .utils.db import get_db_session
from .utils.encryption import VaultManager

__all__ = [
    "get_settings",
    "get_db_session", 
    "VaultManager"
]
