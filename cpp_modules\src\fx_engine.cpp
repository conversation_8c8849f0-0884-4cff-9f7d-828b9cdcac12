/**
 * Foreign Exchange Engine Implementation
 * High-performance currency conversion and FX calculations
 */

#include "../include/fx_engine.h"
#include <algorithm>
#include <cmath>
#include <random>
#include <stdexcept>

FXEngine::FXEngine() {
    initialize_default_rates();
}

FXEngine::~FXEngine() = default;

double FXEngine::convert(double amount, const std::string& from_currency, const std::string& to_currency) {
    if (from_currency == to_currency) {
        return amount;
    }
    
    double rate = get_exchange_rate(from_currency, to_currency);
    return amount * rate;
}

double FXEngine::get_exchange_rate(const std::string& from_currency, const std::string& to_currency) {
    if (from_currency == to_currency) {
        return 1.0;
    }
    
    std::lock_guard<std::mutex> lock(rates_mutex_);
    
    auto key = make_rate_key(from_currency, to_currency);
    auto it = exchange_rates_.find(key);
    
    if (it != exchange_rates_.end()) {
        // Check if rate is stale
        if (!is_rate_stale(from_currency, to_currency)) {
            return it->second;
        }
    }
    
    // Try reverse rate
    auto reverse_key = make_rate_key(to_currency, from_currency);
    auto reverse_it = exchange_rates_.find(reverse_key);
    
    if (reverse_it != exchange_rates_.end()) {
        if (!is_rate_stale(to_currency, from_currency)) {
            return 1.0 / reverse_it->second;
        }
    }
    
    // Try cross rate through USD
    if (from_currency != "USD" && to_currency != "USD") {
        double from_usd_rate = get_exchange_rate(from_currency, "USD");
        double to_usd_rate = get_exchange_rate("USD", to_currency);
        return from_usd_rate * to_usd_rate;
    }
    
    // Default rate if not found
    return 1.0;
}

void FXEngine::update_exchange_rate(const std::string& from_currency, const std::string& to_currency, double rate) {
    std::lock_guard<std::mutex> lock(rates_mutex_);
    
    auto key = make_rate_key(from_currency, to_currency);
    exchange_rates_[key] = rate;
    rate_timestamps_[key] = std::chrono::system_clock::now();
}

std::vector<std::string> FXEngine::get_supported_currencies() const {
    return {
        "USD", "EUR", "GBP", "JPY", "CHF", "CAD", "AUD", "NZD",
        "SEK", "NOK", "DKK", "PLN", "CZK", "HUF", "RUB", "CNY",
        "INR", "BRL", "MXN", "ZAR", "KRW", "SGD", "HKD", "THB"
    };
}

double FXEngine::calculate_cross_rate(const std::string& from_currency, const std::string& to_currency, const std::string& base_currency) {
    if (from_currency == to_currency) {
        return 1.0;
    }
    
    double from_base_rate = get_exchange_rate(from_currency, base_currency);
    double to_base_rate = get_exchange_rate(base_currency, to_currency);
    
    return from_base_rate * to_base_rate;
}

std::vector<double> FXEngine::get_historical_rates(const std::string& from_currency, const std::string& to_currency, int days) {
    // Mock implementation - in production, this would fetch from a database or external API
    std::vector<double> rates;
    double base_rate = get_exchange_rate(from_currency, to_currency);
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::normal_distribution<> dis(0.0, 0.01); // 1% daily volatility
    
    for (int i = 0; i < days; ++i) {
        double variation = dis(gen);
        double rate = base_rate * (1.0 + variation);
        rates.push_back(rate);
        base_rate = rate; // Use previous rate as base for next
    }
    
    return rates;
}

double FXEngine::calculate_volatility(const std::vector<double>& rates) {
    if (rates.size() < 2) {
        return 0.0;
    }
    
    // Calculate returns
    std::vector<double> returns;
    for (size_t i = 1; i < rates.size(); ++i) {
        double return_val = std::log(rates[i] / rates[i-1]);
        returns.push_back(return_val);
    }
    
    // Calculate mean return
    double mean_return = 0.0;
    for (double ret : returns) {
        mean_return += ret;
    }
    mean_return /= returns.size();
    
    // Calculate variance
    double variance = 0.0;
    for (double ret : returns) {
        variance += std::pow(ret - mean_return, 2);
    }
    variance /= (returns.size() - 1);
    
    // Return annualized volatility (assuming daily data)
    return std::sqrt(variance * 252); // 252 trading days per year
}

void FXEngine::refresh_rates() {
    // Mock implementation - in production, this would fetch from external APIs
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis(0.95, 1.05); // ±5% variation
    
    std::lock_guard<std::mutex> lock(rates_mutex_);
    
    for (auto& rate_pair : exchange_rates_) {
        double variation = dis(gen);
        rate_pair.second *= variation;
        rate_timestamps_[rate_pair.first] = std::chrono::system_clock::now();
    }
}

void FXEngine::initialize_default_rates() {
    // Initialize with some default exchange rates (mock data)
    // In production, these would be fetched from a reliable source
    
    // USD base rates
    update_exchange_rate("USD", "EUR", 0.85);
    update_exchange_rate("USD", "GBP", 0.73);
    update_exchange_rate("USD", "JPY", 110.0);
    update_exchange_rate("USD", "CHF", 0.92);
    update_exchange_rate("USD", "CAD", 1.25);
    update_exchange_rate("USD", "AUD", 1.35);
    update_exchange_rate("USD", "NZD", 1.42);
    update_exchange_rate("USD", "SEK", 8.5);
    update_exchange_rate("USD", "NOK", 8.8);
    update_exchange_rate("USD", "DKK", 6.3);
    update_exchange_rate("USD", "PLN", 3.8);
    update_exchange_rate("USD", "CZK", 22.0);
    update_exchange_rate("USD", "HUF", 295.0);
    update_exchange_rate("USD", "RUB", 75.0);
    update_exchange_rate("USD", "CNY", 6.4);
    update_exchange_rate("USD", "INR", 74.0);
    update_exchange_rate("USD", "BRL", 5.2);
    update_exchange_rate("USD", "MXN", 20.0);
    update_exchange_rate("USD", "ZAR", 14.5);
    update_exchange_rate("USD", "KRW", 1180.0);
    update_exchange_rate("USD", "SGD", 1.35);
    update_exchange_rate("USD", "HKD", 7.8);
    update_exchange_rate("USD", "THB", 31.0);
}

bool FXEngine::is_rate_stale(const std::string& from_currency, const std::string& to_currency) const {
    auto key = make_rate_key(from_currency, to_currency);
    auto it = rate_timestamps_.find(key);
    
    if (it == rate_timestamps_.end()) {
        return true;
    }
    
    auto now = std::chrono::system_clock::now();
    auto age = std::chrono::duration_cast<std::chrono::minutes>(now - it->second);
    
    // Consider rates stale after 15 minutes
    return age.count() > 15;
}

std::pair<std::string, std::string> FXEngine::make_rate_key(const std::string& from_currency, const std::string& to_currency) const {
    return std::make_pair(from_currency, to_currency);
}
