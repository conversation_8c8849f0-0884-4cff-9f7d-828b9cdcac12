"""
Tests for configuration management
"""

import pytest
import os
from unittest.mock import patch

from accounting.config import (
    Settings, get_settings, get_vault_config, 
    get_database_config, get_celery_config,
    DevelopmentSettings, ProductionSettings, TestingSettings
)


class TestSettings:
    """Test Settings class"""
    
    def test_default_settings(self):
        """Test default settings values"""
        settings = Settings()
        
        assert settings.app_name == "Enterprise Accounting System"
        assert settings.debug is False
        assert settings.database_url == "postgresql://postgres:password@localhost:5432/accounting"
        assert settings.vault_url == "http://localhost:8200"
        assert settings.default_currency == "USD"
        assert settings.jwt_algorithm == "HS256"
    
    def test_environment_override(self):
        """Test environment variable override"""
        with patch.dict(os.environ, {
            'DEBUG': 'true',
            'DATABASE_URL': 'postgresql://test:test@localhost:5432/test',
            'VAULT_URL': 'http://test:8200'
        }):
            settings = Settings()
            assert settings.debug is True
            assert settings.database_url == 'postgresql://test:test@localhost:5432/test'
            assert settings.vault_url == 'http://test:8200'
    
    def test_development_settings(self):
        """Test development settings"""
        settings = DevelopmentSettings()
        assert settings.debug is True
        assert settings.database_echo is True
        assert settings.log_level == "DEBUG"
    
    def test_production_settings(self):
        """Test production settings"""
        settings = ProductionSettings()
        assert settings.debug is False
        assert settings.database_echo is False
        assert settings.log_level == "WARNING"
    
    def test_testing_settings(self):
        """Test testing settings"""
        settings = TestingSettings()
        assert settings.debug is True
        assert "sqlite" in settings.database_url
        assert settings.vault_token == "test-token"


class TestConfigHelpers:
    """Test configuration helper functions"""
    
    def test_get_vault_config(self):
        """Test Vault configuration helper"""
        with patch('accounting.config.get_settings') as mock_get_settings:
            mock_settings = Settings()
            mock_settings.vault_url = "http://test:8200"
            mock_settings.vault_token = "test-token"
            mock_settings.vault_mount_point = "transit"
            mock_settings.vault_transit_key = "test-key"
            mock_get_settings.return_value = mock_settings
            
            config = get_vault_config()
            
            assert config["url"] == "http://test:8200"
            assert config["token"] == "test-token"
            assert config["mount_point"] == "transit"
            assert config["key_name"] == "test-key"
    
    def test_get_vault_config_with_approle(self):
        """Test Vault configuration with AppRole"""
        with patch('accounting.config.get_settings') as mock_get_settings:
            mock_settings = Settings()
            mock_settings.vault_url = "http://test:8200"
            mock_settings.vault_token = None
            mock_settings.vault_role_id = "test-role-id"
            mock_settings.vault_secret_id = "test-secret-id"
            mock_get_settings.return_value = mock_settings
            
            config = get_vault_config()
            
            assert "token" not in config
            assert config["role_id"] == "test-role-id"
            assert config["secret_id"] == "test-secret-id"
    
    def test_get_database_config(self):
        """Test database configuration helper"""
        with patch('accounting.config.get_settings') as mock_get_settings:
            mock_settings = Settings()
            mock_settings.database_url = "postgresql://test:test@localhost:5432/test"
            mock_settings.database_echo = True
            mock_get_settings.return_value = mock_settings
            
            config = get_database_config()
            
            assert config["url"] == "postgresql://test:test@localhost:5432/test"
            assert config["echo"] is True
            assert config["pool_size"] == 10
            assert config["max_overflow"] == 20
    
    def test_get_celery_config(self):
        """Test Celery configuration helper"""
        with patch('accounting.config.get_settings') as mock_get_settings:
            mock_settings = Settings()
            mock_settings.celery_broker_url = "redis://test:6379/0"
            mock_settings.celery_result_backend = "redis://test:6379/0"
            mock_get_settings.return_value = mock_settings
            
            config = get_celery_config()
            
            assert config["broker_url"] == "redis://test:6379/0"
            assert config["result_backend"] == "redis://test:6379/0"
            assert config["task_serializer"] == "json"
            assert config["timezone"] == "UTC"


class TestSettingsValidation:
    """Test settings validation"""
    
    def test_invalid_database_url(self):
        """Test handling of invalid database URL"""
        # This should not raise an exception, just use the invalid URL
        settings = Settings(database_url="invalid-url")
        assert settings.database_url == "invalid-url"
    
    def test_invalid_port_numbers(self):
        """Test handling of invalid port numbers in URLs"""
        settings = Settings(
            vault_url="http://localhost:invalid",
            rabbitmq_url="amqp://guest:guest@localhost:invalid//"
        )
        # Should accept the URLs as-is, validation happens at connection time
        assert "invalid" in settings.vault_url
        assert "invalid" in settings.rabbitmq_url
    
    def test_empty_secret_keys(self):
        """Test handling of empty secret keys"""
        settings = Settings(secret_key="", jwt_secret_key="")
        # Should accept empty keys, but this would be insecure in production
        assert settings.secret_key == ""
        assert settings.jwt_secret_key == ""


class TestEnvironmentSpecificSettings:
    """Test environment-specific settings"""
    
    def test_get_settings_cached(self):
        """Test that get_settings returns cached instance"""
        settings1 = get_settings()
        settings2 = get_settings()
        assert settings1 is settings2
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'development'})
    def test_development_environment(self):
        """Test development environment detection"""
        from accounting.config import get_settings_for_environment
        settings = get_settings_for_environment()
        assert isinstance(settings, DevelopmentSettings)
        assert settings.debug is True
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'production'})
    def test_production_environment(self):
        """Test production environment detection"""
        from accounting.config import get_settings_for_environment
        settings = get_settings_for_environment()
        assert isinstance(settings, ProductionSettings)
        assert settings.debug is False
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'testing'})
    def test_testing_environment(self):
        """Test testing environment detection"""
        from accounting.config import get_settings_for_environment
        settings = get_settings_for_environment()
        assert isinstance(settings, TestingSettings)
        assert "sqlite" in settings.database_url


class TestConfigurationIntegration:
    """Test configuration integration"""
    
    def test_all_required_settings_present(self):
        """Test that all required settings are present"""
        settings = Settings()
        
        # Database settings
        assert settings.database_url is not None
        assert isinstance(settings.database_echo, bool)
        
        # Vault settings
        assert settings.vault_url is not None
        assert settings.vault_transit_key is not None
        assert settings.vault_mount_point is not None
        
        # Security settings
        assert settings.secret_key is not None
        assert settings.jwt_secret_key is not None
        assert settings.jwt_algorithm is not None
        
        # Application settings
        assert settings.app_name is not None
        assert isinstance(settings.debug, bool)
    
    def test_settings_types(self):
        """Test that settings have correct types"""
        settings = Settings()
        
        assert isinstance(settings.debug, bool)
        assert isinstance(settings.database_echo, bool)
        assert isinstance(settings.jwt_expiration_hours, int)
        assert isinstance(settings.rate_limit_per_minute, int)
        assert isinstance(settings.max_file_size_mb, int)
        assert isinstance(settings.libsodium_key_size, int)
    
    def test_url_formats(self):
        """Test URL format validation"""
        settings = Settings()
        
        # URLs should start with appropriate protocols
        assert settings.vault_url.startswith(('http://', 'https://'))
        assert settings.database_url.startswith(('postgresql://', 'sqlite://'))
        assert settings.rabbitmq_url.startswith('amqp://')
        assert settings.celery_broker_url.startswith(('redis://', 'amqp://'))


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
