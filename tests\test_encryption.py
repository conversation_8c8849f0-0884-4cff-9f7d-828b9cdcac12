"""
Tests for encryption utilities
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import base64

from accounting.utils.encryption import (
    VaultManager, LibsodiumManager, 
    encrypt_field, decrypt_field,
    encrypt_field_sync, decrypt_field_sync,
    init_encryption
)
from accounting.config import Settings


class TestLibsodiumManager:
    """Test libsodium encryption manager"""
    
    def test_key_generation(self):
        """Test encryption key generation"""
        key = LibsodiumManager.generate_key()
        assert len(key) == 32  # 32 bytes for ChaCha20Poly1305
        assert isinstance(key, bytes)
    
    def test_encryption_decryption(self):
        """Test basic encryption and decryption"""
        manager = LibsodiumManager()
        
        plaintext = "sensitive financial data"
        ciphertext = manager.encrypt(plaintext)
        decrypted = manager.decrypt(ciphertext)
        
        assert decrypted == plaintext
        assert ciphertext != plaintext
        assert isinstance(ciphertext, str)
    
    def test_encryption_with_custom_key(self):
        """Test encryption with custom key"""
        key = LibsodiumManager.generate_key()
        manager = LibsodiumManager(key)
        
        plaintext = "test data"
        ciphertext = manager.encrypt(plaintext)
        decrypted = manager.decrypt(ciphertext)
        
        assert decrypted == plaintext
    
    def test_invalid_key_size(self):
        """Test handling of invalid key size"""
        with pytest.raises(ValueError, match="Key must be 32 bytes"):
            LibsodiumManager(b"short_key")
    
    def test_decrypt_invalid_data(self):
        """Test decryption of invalid data"""
        manager = LibsodiumManager()
        
        with pytest.raises(Exception):  # Should raise InvalidMessage or similar
            manager.decrypt("invalid_ciphertext")
    
    def test_encrypt_empty_string(self):
        """Test encryption of empty string"""
        manager = LibsodiumManager()
        
        plaintext = ""
        ciphertext = manager.encrypt(plaintext)
        decrypted = manager.decrypt(ciphertext)
        
        assert decrypted == plaintext
    
    def test_encrypt_unicode_data(self):
        """Test encryption of unicode data"""
        manager = LibsodiumManager()
        
        plaintext = "测试数据 🔒 émojis"
        ciphertext = manager.encrypt(plaintext)
        decrypted = manager.decrypt(ciphertext)
        
        assert decrypted == plaintext


class TestVaultManager:
    """Test Vault encryption manager"""
    
    @pytest.fixture
    def vault_manager(self):
        """Create VaultManager instance for testing"""
        settings = Settings(
            vault_url="http://localhost:8200",
            vault_token="test-token",
            vault_transit_key="test-key",
            vault_mount_point="transit"
        )
        return VaultManager(settings)
    
    @pytest.mark.asyncio
    async def test_vault_initialization_with_token(self, vault_manager):
        """Test Vault initialization with token"""
        with patch('hvac.Client') as mock_client_class:
            mock_client = Mock()
            mock_client.is_authenticated.return_value = True
            mock_client.sys.list_auth_methods.return_value = {"transit/": {}}
            mock_client.secrets.transit.read_key.return_value = {"data": {}}
            mock_client_class.return_value = mock_client
            
            await vault_manager.initialize()
            
            assert vault_manager._authenticated is True
            assert vault_manager.client is mock_client
    
    @pytest.mark.asyncio
    async def test_vault_initialization_with_approle(self):
        """Test Vault initialization with AppRole"""
        settings = Settings(
            vault_url="http://localhost:8200",
            vault_token=None,
            vault_role_id="test-role-id",
            vault_secret_id="test-secret-id"
        )
        vault_manager = VaultManager(settings)
        
        with patch('hvac.Client') as mock_client_class:
            mock_client = Mock()
            mock_client.is_authenticated.return_value = True
            mock_client.auth.approle.login.return_value = {
                'auth': {'client_token': 'approle-token'}
            }
            mock_client.sys.list_auth_methods.return_value = {"transit/": {}}
            mock_client.secrets.transit.read_key.return_value = {"data": {}}
            mock_client_class.return_value = mock_client
            
            await vault_manager.initialize()
            
            assert vault_manager._authenticated is True
            assert vault_manager.client.token == 'approle-token'
    
    @pytest.mark.asyncio
    async def test_vault_encryption_decryption(self, vault_manager):
        """Test Vault encryption and decryption"""
        with patch.object(vault_manager, 'client') as mock_client:
            mock_client.secrets.transit.encrypt_data.return_value = {
                'data': {'ciphertext': 'vault:v1:encrypted_data'}
            }
            mock_client.secrets.transit.decrypt_data.return_value = {
                'data': {'plaintext': base64.b64encode(b'test data').decode()}
            }
            
            vault_manager._authenticated = True
            
            # Test encryption
            plaintext = "test data"
            ciphertext = await vault_manager.encrypt(plaintext)
            assert ciphertext == 'vault:v1:encrypted_data'
            
            # Test decryption
            decrypted = await vault_manager.decrypt(ciphertext)
            assert decrypted == plaintext
    
    @pytest.mark.asyncio
    async def test_vault_key_rotation(self, vault_manager):
        """Test Vault key rotation"""
        with patch.object(vault_manager, 'client') as mock_client:
            vault_manager._authenticated = True
            
            await vault_manager.rotate_key()
            
            mock_client.secrets.transit.rotate_key.assert_called_once_with(
                name=vault_manager.transit_key,
                mount_point=vault_manager.mount_point
            )
    
    @pytest.mark.asyncio
    async def test_vault_authentication_failure(self, vault_manager):
        """Test Vault authentication failure"""
        with patch('hvac.Client') as mock_client_class:
            mock_client = Mock()
            mock_client.is_authenticated.return_value = False
            mock_client_class.return_value = mock_client
            
            with pytest.raises(ValueError, match="Failed to authenticate with Vault"):
                await vault_manager.initialize()
    
    @pytest.mark.asyncio
    async def test_vault_no_auth_method(self):
        """Test Vault with no authentication method"""
        settings = Settings(
            vault_url="http://localhost:8200",
            vault_token=None,
            vault_role_id=None,
            vault_secret_id=None
        )
        vault_manager = VaultManager(settings)
        
        with pytest.raises(ValueError, match="No valid Vault authentication method configured"):
            await vault_manager.initialize()


class TestEncryptionHelpers:
    """Test encryption helper functions"""
    
    @pytest.mark.asyncio
    async def test_encrypt_field_with_vault(self):
        """Test field encryption with Vault"""
        mock_vault_manager = Mock()
        mock_vault_manager.encrypt = AsyncMock(return_value="vault_encrypted")
        
        with patch('accounting.utils.encryption._vault_manager', mock_vault_manager):
            result = await encrypt_field("test_value", use_vault=True)
            assert result == "vault_encrypted"
            mock_vault_manager.encrypt.assert_called_once_with("test_value")
    
    @pytest.mark.asyncio
    async def test_encrypt_field_with_libsodium(self):
        """Test field encryption with libsodium"""
        mock_libsodium_manager = Mock()
        mock_libsodium_manager.encrypt.return_value = "libsodium_encrypted"
        
        with patch('accounting.utils.encryption._libsodium_manager', mock_libsodium_manager):
            result = await encrypt_field("test_value", use_vault=False)
            assert result == "libsodium_encrypted"
            mock_libsodium_manager.encrypt.assert_called_once_with("test_value")
    
    @pytest.mark.asyncio
    async def test_decrypt_field_with_vault(self):
        """Test field decryption with Vault"""
        mock_vault_manager = Mock()
        mock_vault_manager.decrypt = AsyncMock(return_value="decrypted_value")
        
        with patch('accounting.utils.encryption._vault_manager', mock_vault_manager):
            result = await decrypt_field("encrypted_data", use_vault=True)
            assert result == "decrypted_value"
            mock_vault_manager.decrypt.assert_called_once_with("encrypted_data")
    
    @pytest.mark.asyncio
    async def test_encrypt_field_none_value(self):
        """Test encryption of None value"""
        result = await encrypt_field(None)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_decrypt_field_none_value(self):
        """Test decryption of None value"""
        result = await decrypt_field(None)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_encrypt_field_numeric_values(self):
        """Test encryption of numeric values"""
        mock_vault_manager = Mock()
        mock_vault_manager.encrypt = AsyncMock(return_value="encrypted_number")
        
        with patch('accounting.utils.encryption._vault_manager', mock_vault_manager):
            # Test integer
            result = await encrypt_field(1000)
            assert result == "encrypted_number"
            mock_vault_manager.encrypt.assert_called_with("1000")
            
            # Test float
            result = await encrypt_field(1000.50)
            assert result == "encrypted_number"
            mock_vault_manager.encrypt.assert_called_with("1000.5")
    
    def test_encrypt_field_sync(self):
        """Test synchronous field encryption"""
        mock_libsodium_manager = Mock()
        mock_libsodium_manager.encrypt.return_value = "sync_encrypted"
        
        with patch('accounting.utils.encryption._libsodium_manager', mock_libsodium_manager):
            result = encrypt_field_sync("test_value")
            assert result == "sync_encrypted"
            mock_libsodium_manager.encrypt.assert_called_once_with("test_value")
    
    def test_decrypt_field_sync(self):
        """Test synchronous field decryption"""
        mock_libsodium_manager = Mock()
        mock_libsodium_manager.decrypt.return_value = "sync_decrypted"
        
        with patch('accounting.utils.encryption._libsodium_manager', mock_libsodium_manager):
            result = decrypt_field_sync("encrypted_data")
            assert result == "sync_decrypted"
            mock_libsodium_manager.decrypt.assert_called_once_with("encrypted_data")
    
    def test_sync_encryption_no_manager(self):
        """Test sync encryption without manager"""
        with patch('accounting.utils.encryption._libsodium_manager', None):
            with pytest.raises(RuntimeError, match="Libsodium manager not available"):
                encrypt_field_sync("test_value")
    
    @pytest.mark.asyncio
    async def test_encryption_no_manager_available(self):
        """Test encryption when no manager is available"""
        with patch('accounting.utils.encryption._vault_manager', None):
            with patch('accounting.utils.encryption._libsodium_manager', None):
                with pytest.raises(RuntimeError, match="No encryption manager available"):
                    await encrypt_field("test_value")


class TestEncryptionInitialization:
    """Test encryption initialization"""
    
    @pytest.mark.asyncio
    async def test_init_encryption(self):
        """Test encryption initialization"""
        mock_vault_manager = Mock()
        mock_vault_manager.initialize = AsyncMock()
        mock_vault_manager.client.secrets.kv.v2.read_secret_version.side_effect = Exception("Key not found")
        mock_vault_manager.client.secrets.kv.v2.create_or_update_secret = Mock()
        
        with patch('accounting.utils.encryption.LibsodiumManager') as mock_libsodium_class:
            mock_libsodium_class.generate_key.return_value = b'x' * 32
            mock_libsodium_instance = Mock()
            mock_libsodium_class.return_value = mock_libsodium_instance
            
            await init_encryption(mock_vault_manager)
            
            mock_vault_manager.initialize.assert_called_once()
            mock_libsodium_class.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_init_encryption_with_existing_key(self):
        """Test encryption initialization with existing libsodium key"""
        mock_vault_manager = Mock()
        mock_vault_manager.initialize = AsyncMock()
        mock_vault_manager.client.secrets.kv.v2.read_secret_version.return_value = {
            'data': {'data': {'key': base64.b64encode(b'x' * 32).decode()}}
        }
        
        with patch('accounting.utils.encryption.LibsodiumManager') as mock_libsodium_class:
            await init_encryption(mock_vault_manager)
            
            mock_vault_manager.initialize.assert_called_once()
            mock_libsodium_class.assert_called_once_with(b'x' * 32)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
