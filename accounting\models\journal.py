"""
Journal entry models for double-entry bookkeeping
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..utils.db import Base


class JournalEntry(Base):
    """Journal entry header"""
    
    __tablename__ = "journal_entries"
    
    id = Column(Integer, primary_key=True, index=True)
    description = Column(String(500), nullable=False)
    reference = Column(String(100))  # External reference number
    entry_date = Column(DateTime(timezone=True), nullable=False)
    
    # Relationships
    lines = relationship("JournalLine", back_populates="journal_entry", cascade="all, delete-orphan")
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100))
    
    def __repr__(self):
        return f"<JournalEntry(id={self.id}, description='{self.description}', date='{self.entry_date}')>"


class JournalLine(Base):
    """Journal entry line items"""
    
    __tablename__ = "journal_lines"
    
    id = Column(Integer, primary_key=True, index=True)
    journal_entry_id = Column(Integer, ForeignKey("journal_entries.id"), nullable=False)
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=False)
    
    description = Column(String(500))
    
    # Amounts (encrypted)
    debit_amount = Column(Text)  # Encrypted debit amount
    credit_amount = Column(Text)  # Encrypted credit amount
    
    # Currency and FX
    currency = Column(String(3), default="USD")
    fx_rate = Column(Float, default=1.0)
    base_amount = Column(Text)  # Encrypted base currency amount
    
    # Relationships
    journal_entry = relationship("JournalEntry", back_populates="lines")
    account = relationship("Account")
    
    def __repr__(self):
        return f"<JournalLine(id={self.id}, account_id={self.account_id}, journal_entry_id={self.journal_entry_id})>"
