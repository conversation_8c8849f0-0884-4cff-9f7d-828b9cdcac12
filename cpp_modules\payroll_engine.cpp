/**
 * Payroll Engine C++ Module
 * High-performance payroll calculations using pybind11
 */

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include <vector>
#include <map>
#include <string>
#include <cmath>
#include <algorithm>
#include <memory>

#include "include/payroll_calculations.h"
#include "include/tax_calculator.h"
#include "include/benefits_calculator.h"

namespace py = pybind11;

/**
 * Calculate employee payroll with all deductions
 */
std::map<std::string, double> calculate_employee_payroll(
    int employee_id,
    double base_salary,
    double regular_hours,
    double overtime_hours
) {
    PayrollCalculations calc;
    return calc.calculate_full_payroll(employee_id, base_salary, regular_hours, overtime_hours);
}

/**
 * Calculate tax withholding amounts
 */
std::map<std::string, double> calculate_tax_withholding(
    double gross_pay,
    const std::string& filing_status,
    int allowances,
    const std::string& state
) {
    TaxCalculator tax_calc;
    return tax_calc.calculate_withholding(gross_pay, filing_status, allowances, state);
}

/**
 * Calculate benefits deductions
 */
std::map<std::string, double> calculate_benefits_deductions(
    int employee_id,
    double gross_pay,
    const std::map<std::string, py::object>& benefit_elections
) {
    BenefitsCalculator benefits_calc;
    
    // Convert Python objects to C++ types
    std::map<std::string, double> elections;
    for (const auto& election : benefit_elections) {
        try {
            elections[election.first] = election.second.cast<double>();
        } catch (const std::exception& e) {
            // Handle conversion errors
            elections[election.first] = 0.0;
        }
    }
    
    return benefits_calc.calculate_deductions(employee_id, gross_pay, elections);
}

/**
 * Calculate overtime pay with different rates
 */
double calculate_overtime_pay(
    double hourly_rate,
    double overtime_hours,
    double overtime_multiplier = 1.5
) {
    return hourly_rate * overtime_hours * overtime_multiplier;
}

/**
 * Calculate year-to-date totals
 */
std::map<std::string, double> calculate_ytd_totals(
    int employee_id,
    const std::vector<std::map<std::string, double>>& payroll_history
) {
    std::map<std::string, double> ytd_totals = {
        {"gross_pay", 0.0},
        {"net_pay", 0.0},
        {"federal_tax", 0.0},
        {"state_tax", 0.0},
        {"social_security", 0.0},
        {"medicare", 0.0},
        {"total_taxes", 0.0}
    };
    
    for (const auto& payroll : payroll_history) {
        for (auto& total : ytd_totals) {
            auto it = payroll.find(total.first);
            if (it != payroll.end()) {
                total.second += it->second;
            }
        }
    }
    
    return ytd_totals;
}

/**
 * Calculate Social Security tax with wage base limit
 */
double calculate_social_security_tax(double gross_pay, double ytd_gross, double wage_base = 160200.0) {
    const double ss_rate = 0.062; // 6.2%
    
    if (ytd_gross >= wage_base) {
        return 0.0; // Already hit the wage base
    }
    
    double taxable_amount = std::min(gross_pay, wage_base - ytd_gross);
    return taxable_amount * ss_rate;
}

/**
 * Calculate Medicare tax with additional Medicare tax
 */
double calculate_medicare_tax(
    double gross_pay, 
    double ytd_gross, 
    const std::string& filing_status
) {
    const double medicare_rate = 0.0145; // 1.45%
    const double additional_medicare_rate = 0.009; // 0.9%
    
    // Thresholds for additional Medicare tax
    double threshold = 200000.0; // Single
    if (filing_status == "married_joint") {
        threshold = 250000.0;
    } else if (filing_status == "married_separate") {
        threshold = 125000.0;
    }
    
    double regular_medicare = gross_pay * medicare_rate;
    double additional_medicare = 0.0;
    
    if (ytd_gross + gross_pay > threshold) {
        double excess = std::max(0.0, ytd_gross + gross_pay - threshold);
        additional_medicare = std::min(excess, gross_pay) * additional_medicare_rate;
    }
    
    return regular_medicare + additional_medicare;
}

/**
 * Calculate federal income tax using tax brackets
 */
double calculate_federal_income_tax(
    double gross_pay,
    const std::string& filing_status,
    int allowances,
    double additional_withholding = 0.0
) {
    // Simplified federal tax calculation
    // In production, this would use actual IRS tax tables
    
    double allowance_amount = 4300.0 * allowances; // Per allowance
    double taxable_income = std::max(0.0, gross_pay - allowance_amount);
    
    double tax = 0.0;
    
    // Tax brackets for single filers (simplified)
    if (filing_status == "single") {
        if (taxable_income <= 9950) {
            tax = taxable_income * 0.10;
        } else if (taxable_income <= 40525) {
            tax = 995 + (taxable_income - 9950) * 0.12;
        } else if (taxable_income <= 86375) {
            tax = 4664 + (taxable_income - 40525) * 0.22;
        } else {
            tax = 14751 + (taxable_income - 86375) * 0.24;
        }
    } else {
        // Married filing jointly (simplified)
        if (taxable_income <= 19900) {
            tax = taxable_income * 0.10;
        } else if (taxable_income <= 81050) {
            tax = 1990 + (taxable_income - 19900) * 0.12;
        } else if (taxable_income <= 172750) {
            tax = 9328 + (taxable_income - 81050) * 0.22;
        } else {
            tax = 29502 + (taxable_income - 172750) * 0.24;
        }
    }
    
    return tax + additional_withholding;
}

/**
 * Calculate state income tax
 */
double calculate_state_income_tax(
    double gross_pay,
    const std::string& state,
    const std::string& filing_status
) {
    // Simplified state tax calculation
    // In production, this would use actual state tax tables
    
    std::map<std::string, double> state_rates = {
        {"CA", 0.05},   // California
        {"NY", 0.045},  // New York
        {"TX", 0.0},    // Texas (no state income tax)
        {"FL", 0.0},    // Florida (no state income tax)
        {"WA", 0.0},    // Washington (no state income tax)
        {"OR", 0.09},   // Oregon
        {"NV", 0.0},    // Nevada (no state income tax)
        {"DEFAULT", 0.03} // Default rate for other states
    };
    
    auto it = state_rates.find(state);
    double rate = (it != state_rates.end()) ? it->second : state_rates["DEFAULT"];
    
    return gross_pay * rate;
}

/**
 * Calculate 401(k) contribution with limits
 */
double calculate_401k_contribution(
    double gross_pay,
    double contribution_percentage,
    double ytd_contributions,
    double annual_limit = 22500.0
) {
    double contribution = gross_pay * (contribution_percentage / 100.0);
    double remaining_limit = annual_limit - ytd_contributions;
    
    return std::min(contribution, remaining_limit);
}

/**
 * Calculate health insurance premium
 */
double calculate_health_insurance_premium(
    const std::string& plan_type,
    const std::string& coverage_level,
    double employee_contribution_percentage = 0.8
) {
    // Simplified health insurance calculation
    std::map<std::string, std::map<std::string, double>> premiums = {
        {"basic", {{"employee", 200.0}, {"family", 600.0}}},
        {"standard", {{"employee", 300.0}, {"family", 900.0}}},
        {"premium", {{"employee", 400.0}, {"family", 1200.0}}}
    };
    
    auto plan_it = premiums.find(plan_type);
    if (plan_it == premiums.end()) {
        return 0.0;
    }
    
    auto coverage_it = plan_it->second.find(coverage_level);
    if (coverage_it == plan_it->second.end()) {
        return 0.0;
    }
    
    return coverage_it->second * employee_contribution_percentage;
}

/**
 * Batch payroll processing for multiple employees
 */
std::vector<std::map<std::string, double>> process_batch_payroll(
    const std::vector<std::map<std::string, py::object>>& employee_data
) {
    std::vector<std::map<std::string, double>> results;
    
    for (const auto& employee : employee_data) {
        try {
            int employee_id = employee.at("employee_id").cast<int>();
            double base_salary = employee.at("base_salary").cast<double>();
            double regular_hours = employee.at("regular_hours").cast<double>();
            double overtime_hours = employee.at("overtime_hours").cast<double>();
            
            auto payroll_result = calculate_employee_payroll(
                employee_id, base_salary, regular_hours, overtime_hours
            );
            
            results.push_back(payroll_result);
            
        } catch (const std::exception& e) {
            // Handle individual employee errors
            std::map<std::string, double> error_result = {
                {"error", 1.0},
                {"gross_pay", 0.0},
                {"net_pay", 0.0}
            };
            results.push_back(error_result);
        }
    }
    
    return results;
}

// Python module definition
PYBIND11_MODULE(payroll_engine, m) {
    m.doc() = "High-performance payroll engine for Enterprise Accounting System";
    
    // Main payroll calculations
    m.def("calculate_employee_payroll", &calculate_employee_payroll,
          "Calculate complete employee payroll",
          py::arg("employee_id"), py::arg("base_salary"), 
          py::arg("regular_hours"), py::arg("overtime_hours"));
    
    m.def("calculate_tax_withholding", &calculate_tax_withholding,
          "Calculate tax withholding amounts",
          py::arg("gross_pay"), py::arg("filing_status"), 
          py::arg("allowances"), py::arg("state"));
    
    m.def("calculate_benefits_deductions", &calculate_benefits_deductions,
          "Calculate benefits deductions",
          py::arg("employee_id"), py::arg("gross_pay"), py::arg("benefit_elections"));
    
    // Specific calculations
    m.def("calculate_overtime_pay", &calculate_overtime_pay,
          "Calculate overtime pay",
          py::arg("hourly_rate"), py::arg("overtime_hours"), 
          py::arg("overtime_multiplier") = 1.5);
    
    m.def("calculate_social_security_tax", &calculate_social_security_tax,
          "Calculate Social Security tax with wage base limit",
          py::arg("gross_pay"), py::arg("ytd_gross"), py::arg("wage_base") = 160200.0);
    
    m.def("calculate_medicare_tax", &calculate_medicare_tax,
          "Calculate Medicare tax with additional Medicare tax",
          py::arg("gross_pay"), py::arg("ytd_gross"), py::arg("filing_status"));
    
    m.def("calculate_federal_income_tax", &calculate_federal_income_tax,
          "Calculate federal income tax",
          py::arg("gross_pay"), py::arg("filing_status"), 
          py::arg("allowances"), py::arg("additional_withholding") = 0.0);
    
    m.def("calculate_state_income_tax", &calculate_state_income_tax,
          "Calculate state income tax",
          py::arg("gross_pay"), py::arg("state"), py::arg("filing_status"));
    
    // Benefits calculations
    m.def("calculate_401k_contribution", &calculate_401k_contribution,
          "Calculate 401(k) contribution with limits",
          py::arg("gross_pay"), py::arg("contribution_percentage"), 
          py::arg("ytd_contributions"), py::arg("annual_limit") = 22500.0);
    
    m.def("calculate_health_insurance_premium", &calculate_health_insurance_premium,
          "Calculate health insurance premium",
          py::arg("plan_type"), py::arg("coverage_level"), 
          py::arg("employee_contribution_percentage") = 0.8);
    
    // Batch processing
    m.def("process_batch_payroll", &process_batch_payroll,
          "Process payroll for multiple employees",
          py::arg("employee_data"));
    
    m.def("calculate_ytd_totals", &calculate_ytd_totals,
          "Calculate year-to-date totals",
          py::arg("employee_id"), py::arg("payroll_history"));
}
