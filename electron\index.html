<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Accounting & Financial Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .status {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }

        .status.loading {
            border-left-color: #ffc107;
        }

        .status.error {
            border-left-color: #dc3545;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: left;
        }

        .feature h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .feature p {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .version-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">EA</div>
        <h1>Enterprise Accounting</h1>
        <p class="subtitle">High-Performance Financial Management System</p>
        
        <div id="status" class="status loading">
            <div class="loading-spinner"></div>
            <span id="status-text">Initializing Python backend...</span>
        </div>

        <div class="features">
            <div class="feature">
                <h3>🏦 General Ledger</h3>
                <p>Complete double-entry bookkeeping with real-time balance calculations powered by C++ engines.</p>
            </div>
            <div class="feature">
                <h3>💰 Payroll Management</h3>
                <p>Automated payroll processing with tax calculations, benefits deductions, and compliance reporting.</p>
            </div>
            <div class="feature">
                <h3>📊 Financial Reports</h3>
                <p>Generate balance sheets, income statements, cash flow reports, and custom financial analytics.</p>
            </div>
            <div class="feature">
                <h3>🔒 Enterprise Security</h3>
                <p>Field-level encryption with HashiCorp Vault and libsodium for maximum data protection.</p>
            </div>
        </div>

        <div>
            <button id="launch-btn" class="btn" disabled>
                <span id="launch-text">Connecting to Backend...</span>
            </button>
            <button id="restart-btn" class="btn" onclick="restartBackend()" style="display: none;">
                Restart Backend
            </button>
        </div>

        <div class="version-info">
            <div>Version: <span id="app-version">1.0.0</span></div>
            <div>Backend Port: <span id="backend-port">8000</span></div>
            <div>Built with Python 3.11 + C++ + Electron.js</div>
        </div>
    </div>

    <script>
        let backendPort = 8000;
        let healthCheckInterval;

        // Initialize the application
        async function initialize() {
            try {
                // Get app version and backend port from main process
                if (window.electronAPI) {
                    const version = await window.electronAPI.getAppVersion();
                    const port = await window.electronAPI.getPythonPort();
                    
                    document.getElementById('app-version').textContent = version;
                    document.getElementById('backend-port').textContent = port;
                    backendPort = port;
                }

                // Start health checking
                startHealthCheck();

            } catch (error) {
                console.error('Initialization error:', error);
                updateStatus('error', 'Failed to initialize application');
            }
        }

        // Check backend health
        async function checkBackendHealth() {
            try {
                const response = await fetch(`http://localhost:${backendPort}/health`, {
                    method: 'GET',
                    timeout: 5000
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('success', 'Backend connected successfully');
                    enableLaunchButton();
                    return true;
                } else {
                    throw new Error(`Backend returned status ${response.status}`);
                }
            } catch (error) {
                console.error('Health check failed:', error);
                updateStatus('error', 'Backend connection failed - ' + error.message);
                disableLaunchButton();
                return false;
            }
        }

        // Start periodic health checking
        function startHealthCheck() {
            // Initial check
            checkBackendHealth();

            // Periodic checks every 5 seconds
            healthCheckInterval = setInterval(checkBackendHealth, 5000);
        }

        // Update status display
        function updateStatus(type, message) {
            const statusDiv = document.getElementById('status');
            const statusText = document.getElementById('status-text');
            
            statusDiv.className = `status ${type}`;
            statusText.textContent = message;

            // Remove loading spinner for success/error states
            if (type !== 'loading') {
                const spinner = statusDiv.querySelector('.loading-spinner');
                if (spinner) {
                    spinner.remove();
                }
            }
        }

        // Enable launch button
        function enableLaunchButton() {
            const launchBtn = document.getElementById('launch-btn');
            const launchText = document.getElementById('launch-text');
            const restartBtn = document.getElementById('restart-btn');
            
            launchBtn.disabled = false;
            launchText.textContent = 'Launch Application';
            launchBtn.onclick = launchApplication;
            restartBtn.style.display = 'inline-block';
        }

        // Disable launch button
        function disableLaunchButton() {
            const launchBtn = document.getElementById('launch-btn');
            const launchText = document.getElementById('launch-text');
            const restartBtn = document.getElementById('restart-btn');
            
            launchBtn.disabled = true;
            launchText.textContent = 'Backend Unavailable';
            launchBtn.onclick = null;
            restartBtn.style.display = 'none';
        }

        // Launch the main application
        function launchApplication() {
            // Clear health check interval
            if (healthCheckInterval) {
                clearInterval(healthCheckInterval);
            }

            // Navigate to the backend application
            window.location.href = `http://localhost:${backendPort}`;
        }

        // Restart backend
        async function restartBackend() {
            updateStatus('loading', 'Restarting backend...');
            
            try {
                if (window.electronAPI) {
                    await window.electronAPI.restartBackend();
                    
                    // Wait a moment then start health checking again
                    setTimeout(() => {
                        startHealthCheck();
                    }, 3000);
                }
            } catch (error) {
                console.error('Failed to restart backend:', error);
                updateStatus('error', 'Failed to restart backend');
            }
        }

        // Handle menu actions from main process
        if (window.electronAPI) {
            window.electronAPI.onMenuAction((action, data) => {
                console.log('Menu action received:', action, data);
                
                switch (action) {
                    case 'new-journal-entry':
                        if (document.getElementById('launch-btn').disabled === false) {
                            window.location.href = `http://localhost:${backendPort}/journal/new`;
                        }
                        break;
                    case 'balance-sheet':
                        if (document.getElementById('launch-btn').disabled === false) {
                            window.location.href = `http://localhost:${backendPort}/report/balance-sheet`;
                        }
                        break;
                    case 'income-statement':
                        if (document.getElementById('launch-btn').disabled === false) {
                            window.location.href = `http://localhost:${backendPort}/report/income-statement`;
                        }
                        break;
                    case 'settings':
                        if (document.getElementById('launch-btn').disabled === false) {
                            window.location.href = `http://localhost:${backendPort}/settings`;
                        }
                        break;
                }
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initialize);

        // Handle window unload
        window.addEventListener('beforeunload', () => {
            if (healthCheckInterval) {
                clearInterval(healthCheckInterval);
            }
        });
    </script>
</body>
</html>
