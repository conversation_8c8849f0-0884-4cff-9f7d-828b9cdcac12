#!/usr/bin/env python3
"""
Enterprise Accounting & Financial Management System
Main application entry point with FastAPI server and C++ integration
"""

import asyncio
import logging
import os
import sys
import subprocess
from pathlib import Path
from typing import Optional

import click
import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import accounting modules
from accounting.config import get_settings, Settings
from accounting.utils.db import init_database, get_db_session
from accounting.utils.encryption import VaultManager, init_encryption
from accounting.services.auth import AuthService
from accounting.services.ledger import LedgerService
from accounting.services.payroll import PayrollService
from accounting.services.report import ReportService
from accounting.tasks import init_celery

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global app instance
app = FastAPI(
    title="Enterprise Accounting & Financial Management",
    description="High-performance accounting system with C++ engines",
    version="1.0.0"
)

# CORS middleware for Electron frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global services
vault_manager: Optional[VaultManager] = None
auth_service: Optional[AuthService] = None
ledger_service: Optional[LedgerService] = None
payroll_service: Optional[PayrollService] = None
report_service: Optional[ReportService] = None


def get_version():
    """Get application version"""
    return "1.0.0"


def setup_logging(debug: bool = False):
    """Setup application logging"""
    level = logging.DEBUG if debug else logging.INFO

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # Configure basic logging
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        force=True  # Override existing configuration
    )


def get_system_info():
    """Get system information"""
    try:
        import psutil
        return {
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "disk_total": psutil.disk_usage('/').total
        }
    except ImportError:
        return {
            "cpu_count": "unknown",
            "memory_total": "unknown",
            "disk_total": "unknown"
        }


def validate_environment():
    """Validate environment setup"""
    required_files = [".env", "requirements.txt"]
    for file_path in required_files:
        if not Path(file_path).exists():
            logger.error(f"Required file missing: {file_path}")
            sys.exit(1)


def build_cpp_modules(parallel_jobs: int = 4):
    """Build C++ modules using CMake and pybind11"""
    logger.info("Building C++ modules...")

    cpp_dir = Path("cpp_modules")
    build_dir = cpp_dir / "build"

    # Check if CMake is available
    if not cpp_dir.exists():
        logger.error("cpp_modules directory not found")
        return False

    build_dir.mkdir(exist_ok=True)

    try:
        # Configure with CMake
        result = subprocess.run([
            "cmake", "-S", str(cpp_dir), "-B", str(build_dir),
            "-DCMAKE_BUILD_TYPE=Release"
        ], check=True, cwd=".")

        if result.returncode != 0:
            return False

        # Build with parallel jobs
        result = subprocess.run([
            "cmake", "--build", str(build_dir), "--config", "Release",
            "--", f"-j{parallel_jobs}"
        ], check=True, cwd=".")

        if result.returncode != 0:
            return False

        # Add build directory to Python path for imports
        sys.path.insert(0, str(build_dir))

        logger.info("C++ modules built successfully")
        return True

    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        logger.error(f"Failed to build C++ modules: {e}")
        return False


async def init_services():
    """Initialize all application services"""
    global vault_manager, auth_service, ledger_service, payroll_service, report_service
    
    settings = get_settings()
    
    # Initialize Vault and encryption
    vault_manager = VaultManager(settings)
    await init_encryption(vault_manager)
    
    # Initialize database
    await init_database(settings)
    
    # Initialize Celery for background tasks
    init_celery(settings)
    
    # Initialize services
    auth_service = AuthService(vault_manager)
    ledger_service = LedgerService(vault_manager)
    payroll_service = PayrollService(vault_manager)
    report_service = ReportService(vault_manager)
    
    logger.info("All services initialized successfully")


@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    logger.info("Starting Enterprise Accounting System...")
    
    # Build C++ modules first
    build_cpp_modules()
    
    # Initialize services
    await init_services()
    
    logger.info("Application startup complete")


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event"""
    logger.info("Shutting down Enterprise Accounting System...")


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Enterprise Accounting System"}


# Authentication endpoints
@app.post("/auth/login")
async def login(credentials: dict):
    """User authentication"""
    try:
        token = await auth_service.authenticate(
            credentials.get("username"),
            credentials.get("password")
        )
        return {"access_token": token, "token_type": "bearer"}
    except Exception as e:
        raise HTTPException(status_code=401, detail=str(e))


# Journal/Ledger endpoints
@app.post("/journal")
async def create_journal_entry(entry: dict, db_session=Depends(get_db_session)):
    """Create a new journal entry"""
    try:
        result = await ledger_service.create_journal_entry(entry, db_session)
        return {"success": True, "entry_id": result}
    except Exception as e:
        logger.error(f"Journal entry creation failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/journal/{entry_id}")
async def get_journal_entry(entry_id: int, db_session=Depends(get_db_session)):
    """Retrieve a journal entry"""
    try:
        entry = await ledger_service.get_journal_entry(entry_id, db_session)
        return entry
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


# Payroll endpoints
@app.post("/payroll")
async def process_payroll(payroll_data: dict, db_session=Depends(get_db_session)):
    """Process payroll calculations"""
    try:
        result = await payroll_service.process_payroll(payroll_data, db_session)
        return {"success": True, "payroll_id": result}
    except Exception as e:
        logger.error(f"Payroll processing failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/payroll/{payroll_id}")
async def get_payroll(payroll_id: int, db_session=Depends(get_db_session)):
    """Retrieve payroll information"""
    try:
        payroll = await payroll_service.get_payroll(payroll_id, db_session)
        return payroll
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


# Reporting endpoints
@app.get("/report/balance-sheet")
async def generate_balance_sheet(
    as_of_date: str = None,
    db_session=Depends(get_db_session)
):
    """Generate balance sheet report"""
    try:
        report = await report_service.generate_balance_sheet(as_of_date, db_session)
        return report
    except Exception as e:
        logger.error(f"Balance sheet generation failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/report/income-statement")
async def generate_income_statement(
    start_date: str,
    end_date: str,
    db_session=Depends(get_db_session)
):
    """Generate income statement report"""
    try:
        report = await report_service.generate_income_statement(
            start_date, end_date, db_session
        )
        return report
    except Exception as e:
        logger.error(f"Income statement generation failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@click.group()
@click.version_option(version=get_version())
def cli():
    """Enterprise Accounting System CLI"""
    pass


@cli.command()
@click.option("--host", default="127.0.0.1", help="Host to bind to")
@click.option("--port", default=8000, help="Port to bind to")
@click.option("--reload", is_flag=True, help="Enable auto-reload")
def serve(host: str, port: int, reload: bool):
    """Start the FastAPI server"""
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


async def init_database_wrapper():
    """Wrapper for async database initialization"""
    settings = get_settings()
    await init_database(settings)


async def init_encryption_wrapper():
    """Wrapper for async encryption initialization"""
    settings = get_settings()
    vault_manager = VaultManager(settings)
    await init_encryption(vault_manager)


@cli.command()
def init_db():
    """Initialize the database"""
    try:
        asyncio.run(init_database_wrapper())
        click.echo("Database initialized successfully")
    except Exception as e:
        click.echo(f"Database initialization failed: {e}", err=True)
        sys.exit(1)


@cli.command()
def build_modules():
    """Build C++ modules"""
    try:
        result = build_cpp_modules()
        if result:
            click.echo("C++ modules built successfully")
        else:
            click.echo("C++ modules build failed", err=True)
            sys.exit(1)
    except Exception as e:
        click.echo(f"Build failed: {e}", err=True)
        sys.exit(1)


@cli.command()
def health():
    """Check system health"""
    click.echo("System Status Check")
    click.echo("==================")

    # Check Python version
    click.echo(f"Python Version: {sys.version}")

    # Check system info
    info = get_system_info()
    for key, value in info.items():
        click.echo(f"{key.replace('_', ' ').title()}: {value}")

    # Check required files
    try:
        validate_environment()
        click.echo("Environment: OK")
    except SystemExit:
        click.echo("Environment: FAILED")
        return

    click.echo("Overall Status: HEALTHY")


@cli.command()
@click.option('--host', default='127.0.0.1', help='Host to bind to')
@click.option('--port', default=8000, help='Port to bind to')
@click.option('--reload', default=False, help='Enable auto-reload')
def serve(host, port, reload):
    """Start the FastAPI server"""
    try:
        # Initialize services before starting server
        setup_logging()
        click.echo("Initializing services...")

        # Initialize database
        asyncio.run(init_database_wrapper())
        click.echo("Database initialized")

        # Initialize encryption
        asyncio.run(init_encryption_wrapper())
        click.echo("Encryption initialized")

        click.echo(f"Starting server on {host}:{port}")
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except Exception as e:
        click.echo(f"Server startup failed: {e}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    cli()
