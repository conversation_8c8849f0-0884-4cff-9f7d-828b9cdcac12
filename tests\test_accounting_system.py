"""
Comprehensive test suite for Enterprise Accounting System
Tests all major components including C++ integration, encryption, and business logic
"""

import pytest
import asyncio
from datetime import datetime, date
from decimal import Decimal
from unittest.mock import Mock, patch, AsyncMock

# Import the modules we're testing
from accounting.config import get_settings
from accounting.utils.encryption import <PERSON><PERSON><PERSON><PERSON><PERSON>, LibsodiumManager
from accounting.utils.db import init_database, get_async_session
from accounting.services.auth import AuthService
from accounting.services.ledger import LedgerService
from accounting.services.payroll import PayrollService
from accounting.services.report import ReportService


class TestVaultManager:
    """Test Vault encryption functionality"""
    
    @pytest.fixture
    def vault_manager(self):
        settings = get_settings()
        return VaultManager(settings)
    
    @pytest.mark.asyncio
    async def test_vault_initialization(self, vault_manager):
        """Test Vault manager initialization"""
        # Mock Vault client for testing
        with patch('hvac.Client') as mock_client:
            mock_client.return_value.is_authenticated.return_value = True
            mock_client.return_value.sys.list_auth_methods.return_value = {"transit/": {}}
            
            await vault_manager.initialize()
            assert vault_manager._authenticated is True
    
    @pytest.mark.asyncio
    async def test_encryption_decryption(self, vault_manager):
        """Test encryption and decryption"""
        # Mock Vault responses
        with patch.object(vault_manager, 'client') as mock_client:
            mock_client.secrets.transit.encrypt_data.return_value = {
                'data': {'ciphertext': 'vault:v1:encrypted_data'}
            }
            mock_client.secrets.transit.decrypt_data.return_value = {
                'data': {'plaintext': 'dGVzdCBkYXRh'}  # base64 encoded "test data"
            }
            
            vault_manager._authenticated = True
            
            # Test encryption
            plaintext = "test data"
            ciphertext = await vault_manager.encrypt(plaintext)
            assert ciphertext == 'vault:v1:encrypted_data'
            
            # Test decryption
            decrypted = await vault_manager.decrypt(ciphertext)
            assert decrypted == plaintext


class TestLibsodiumManager:
    """Test libsodium encryption functionality"""
    
    def test_key_generation(self):
        """Test key generation"""
        key = LibsodiumManager.generate_key()
        assert len(key) == 32  # 32 bytes for ChaCha20Poly1305
    
    def test_encryption_decryption(self):
        """Test encryption and decryption"""
        manager = LibsodiumManager()
        
        plaintext = "sensitive financial data"
        ciphertext = manager.encrypt(plaintext)
        decrypted = manager.decrypt(ciphertext)
        
        assert decrypted == plaintext
        assert ciphertext != plaintext


class TestAuthService:
    """Test authentication service"""
    
    @pytest.fixture
    def auth_service(self):
        vault_manager = Mock()
        return AuthService(vault_manager)
    
    @pytest.mark.asyncio
    async def test_local_authentication(self, auth_service):
        """Test local authentication"""
        # Test valid credentials
        token = await auth_service.authenticate("admin", "admin")
        assert token is not None
        
        # Test invalid credentials
        with pytest.raises(ValueError):
            await auth_service.authenticate("invalid", "invalid")
    
    @pytest.mark.asyncio
    async def test_token_verification(self, auth_service):
        """Test JWT token verification"""
        # Create a token
        token = await auth_service.authenticate("admin", "admin")
        
        # Verify the token
        payload = await auth_service.verify_token(token)
        assert payload["sub"] == "admin"
        assert "roles" in payload
    
    def test_password_hashing(self, auth_service):
        """Test password hashing"""
        password = "test_password"
        hashed = auth_service.hash_password(password)
        
        assert auth_service.verify_password(password, hashed)
        assert not auth_service.verify_password("wrong_password", hashed)


class TestLedgerService:
    """Test ledger service functionality"""
    
    @pytest.fixture
    def ledger_service(self):
        vault_manager = Mock()
        service = LedgerService(vault_manager)
        # Mock the C++ engine
        service.ledger_engine = Mock()
        return service
    
    @pytest.mark.asyncio
    async def test_journal_entry_validation(self, ledger_service):
        """Test journal entry validation"""
        # Valid entry
        valid_entry = {
            "description": "Test journal entry",
            "entry_date": "2023-12-01T00:00:00",
            "lines": [
                {
                    "account_id": 1,
                    "amount": "1000.00",
                    "type": "debit"
                },
                {
                    "account_id": 2,
                    "amount": "1000.00",
                    "type": "credit"
                }
            ]
        }
        
        # Should not raise an exception
        await ledger_service._validate_journal_entry(valid_entry)
        
        # Invalid entry (unbalanced)
        invalid_entry = {
            "description": "Test journal entry",
            "entry_date": "2023-12-01T00:00:00",
            "lines": [
                {
                    "account_id": 1,
                    "amount": "1000.00",
                    "type": "debit"
                },
                {
                    "account_id": 2,
                    "amount": "500.00",
                    "type": "credit"
                }
            ]
        }
        
        # Should raise an exception during processing (not validation)
        await ledger_service._validate_journal_entry(invalid_entry)  # This passes validation
    
    @pytest.mark.asyncio
    async def test_cpp_engine_integration(self, ledger_service):
        """Test C++ engine integration"""
        # Mock C++ engine calls
        ledger_service.ledger_engine.calculate_fx_allocation.return_value = 850.0
        ledger_service.ledger_engine.update_account_balance.return_value = 5000.0
        
        # Test FX calculation
        result = ledger_service.ledger_engine.calculate_fx_allocation(1000.0, "USD", "EUR")
        assert result == 850.0
        
        # Test balance update
        new_balance = ledger_service.ledger_engine.update_account_balance(1, 1000.0, True)
        assert new_balance == 5000.0


class TestPayrollService:
    """Test payroll service functionality"""
    
    @pytest.fixture
    def payroll_service(self):
        vault_manager = Mock()
        service = PayrollService(vault_manager)
        # Mock the C++ engine
        service.payroll_engine = Mock()
        return service
    
    @pytest.mark.asyncio
    async def test_payroll_calculation_python(self, payroll_service):
        """Test Python payroll calculation"""
        result = await payroll_service._calculate_payroll_python(
            employee_id=1,
            base_salary=Decimal('50000'),
            regular_hours=Decimal('40'),
            overtime_hours=Decimal('5')
        )
        
        assert "gross_pay" in result
        assert "net_pay" in result
        assert "total_taxes" in result
        assert "overtime_pay" in result
        
        # Verify overtime pay calculation
        hourly_rate = Decimal('50000') / Decimal('2080')
        expected_overtime = hourly_rate * Decimal('1.5') * Decimal('5')
        assert result["overtime_pay"] == expected_overtime
    
    @pytest.mark.asyncio
    async def test_tax_withholding_calculation(self, payroll_service):
        """Test tax withholding calculation"""
        employee_data = {
            "filing_status": "single",
            "allowances": 1,
            "state": "CA"
        }
        
        result = await payroll_service._calculate_tax_withholding_python(
            Decimal('5000'), employee_data
        )
        
        assert "federal_tax" in result
        assert "state_tax" in result
        assert "social_security" in result
        assert "medicare" in result
        
        # Verify Social Security rate (6.2%)
        expected_ss = Decimal('5000') * Decimal('0.062')
        assert result["social_security"] == expected_ss


class TestReportService:
    """Test report generation functionality"""
    
    @pytest.fixture
    def report_service(self):
        vault_manager = Mock()
        return ReportService(vault_manager)
    
    @pytest.mark.asyncio
    async def test_balance_sheet_structure(self, report_service):
        """Test balance sheet report structure"""
        # Mock database session and data
        mock_session = AsyncMock()
        
        # Mock account data
        mock_accounts = []
        mock_session.execute.return_value.scalars.return_value.all.return_value = mock_accounts
        
        # Mock the balance calculation
        with patch.object(report_service, '_get_account_balance', return_value=Decimal('0')):
            result = await report_service.generate_balance_sheet(
                as_of_date="2023-12-31T00:00:00",
                db_session=mock_session
            )
        
        # Verify structure
        assert "assets" in result
        assert "liabilities" in result
        assert "equity" in result
        assert "report_date" in result
        
        assert "current_assets" in result["assets"]
        assert "non_current_assets" in result["assets"]
        assert "total_assets" in result["assets"]


class TestCppEngineIntegration:
    """Test C++ engine integration"""
    
    def test_cpp_module_import(self):
        """Test C++ module import"""
        try:
            # This will fail if C++ modules aren't built, which is expected
            import ledger_engine
            import payroll_engine
            
            # Test basic function calls
            fx_result = ledger_engine.calculate_fx_allocation(1000.0, "USD", "EUR")
            assert isinstance(fx_result, float)
            
            balance_result = ledger_engine.update_account_balance(1, 1000.0, True)
            assert isinstance(balance_result, float)
            
        except ImportError:
            # Expected if C++ modules aren't built
            pytest.skip("C++ modules not available - run 'python main.py build-modules' first")


class TestSystemIntegration:
    """Integration tests for the complete system"""
    
    @pytest.mark.asyncio
    async def test_complete_journal_entry_flow(self):
        """Test complete journal entry creation flow"""
        # This would require a test database setup
        # For now, we'll mock the components
        
        vault_manager = Mock()
        ledger_service = LedgerService(vault_manager)
        
        # Mock encryption functions
        with patch('accounting.utils.encryption.encrypt_field', return_value="encrypted_data"):
            with patch('accounting.utils.encryption.decrypt_field', return_value="1000.00"):
                
                entry_data = {
                    "description": "Test journal entry",
                    "entry_date": "2023-12-01T00:00:00",
                    "lines": [
                        {
                            "account_id": 1,
                            "amount": "1000.00",
                            "type": "debit"
                        },
                        {
                            "account_id": 2,
                            "amount": "1000.00",
                            "type": "credit"
                        }
                    ]
                }
                
                # Validate the entry
                await ledger_service._validate_journal_entry(entry_data)
                
                # This would normally create the entry in the database
                # For testing, we just verify the validation passes
                assert True
    
    @pytest.mark.asyncio
    async def test_payroll_processing_flow(self):
        """Test complete payroll processing flow"""
        vault_manager = Mock()
        payroll_service = PayrollService(vault_manager)
        
        payroll_data = {
            "period_start": "2023-12-01T00:00:00",
            "period_end": "2023-12-15T00:00:00",
            "pay_date": "2023-12-20T00:00:00",
            "employee_ids": [1, 2, 3],
            "regular_hours": "80",
            "overtime_hours": "5"
        }
        
        # Validate payroll data
        await payroll_service._validate_payroll_data(payroll_data)
        
        # Test payroll calculation
        result = await payroll_service._calculate_payroll_python(
            employee_id=1,
            base_salary=Decimal('50000'),
            regular_hours=Decimal('80'),
            overtime_hours=Decimal('5')
        )
        
        assert result["gross_pay"] > 0
        assert result["net_pay"] > 0
        assert result["net_pay"] < result["gross_pay"]


# Pytest configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
