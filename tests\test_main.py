"""
Tests for main application entry point
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Import main module
import main


class TestMainApplication:
    """Test main application functionality"""
    
    def test_cli_help(self):
        """Test CLI help command"""
        runner = CliRunner()
        result = runner.invoke(main.cli, ['--help'])
        assert result.exit_code == 0
        assert "Enterprise Accounting System" in result.output
    
    def test_cli_version(self):
        """Test CLI version command"""
        runner = CliRunner()
        result = runner.invoke(main.cli, ['--version'])
        assert result.exit_code == 0
        assert "1.0.0" in result.output
    
    @patch('main.init_database_wrapper')
    def test_init_db_command(self, mock_init_db):
        """Test database initialization command"""
        mock_init_db.return_value = asyncio.Future()
        mock_init_db.return_value.set_result(None)

        runner = CliRunner()
        result = runner.invoke(main.cli, ['init-db'])
        assert result.exit_code == 0
    
    @patch('main.build_cpp_modules')
    def test_build_modules_command(self, mock_build):
        """Test C++ modules build command"""
        mock_build.return_value = True
        
        runner = CliRunner()
        result = runner.invoke(main.cli, ['build-modules'])
        assert result.exit_code == 0
        mock_build.assert_called_once()
    
    @patch('main.build_cpp_modules')
    def test_build_modules_failure(self, mock_build):
        """Test C++ modules build failure"""
        mock_build.return_value = False
        
        runner = CliRunner()
        result = runner.invoke(main.cli, ['build-modules'])
        assert result.exit_code == 1
        mock_build.assert_called_once()
    
    @patch('uvicorn.run')
    @patch('main.init_encryption_wrapper')
    @patch('main.init_database_wrapper')
    def test_serve_command(self, mock_init_db, mock_init_encryption, mock_uvicorn):
        """Test serve command"""
        mock_init_db.return_value = asyncio.Future()
        mock_init_db.return_value.set_result(None)
        mock_init_encryption.return_value = asyncio.Future()
        mock_init_encryption.return_value.set_result(None)

        runner = CliRunner()
        result = runner.invoke(main.cli, ['serve', '--host', '127.0.0.1', '--port', '8001'])
        assert result.exit_code == 0
        mock_uvicorn.assert_called_once()
    
    def test_health_check_command(self):
        """Test health check command"""
        runner = CliRunner()
        result = runner.invoke(main.cli, ['health'])
        assert result.exit_code == 0
        assert "System Status" in result.output


class TestCppModuleBuild:
    """Test C++ module building functionality"""
    
    @patch('subprocess.run')
    @patch('os.path.exists')
    @patch('os.makedirs')
    def test_build_cpp_modules_success(self, mock_makedirs, mock_exists, mock_subprocess):
        """Test successful C++ module build"""
        mock_exists.return_value = True
        mock_subprocess.return_value.returncode = 0
        
        result = main.build_cpp_modules()
        assert result is True
        mock_subprocess.assert_called()
    
    @patch('subprocess.run')
    @patch('os.path.exists')
    def test_build_cpp_modules_cmake_failure(self, mock_exists, mock_subprocess):
        """Test C++ module build with CMake failure"""
        mock_exists.return_value = True
        mock_subprocess.return_value.returncode = 1
        
        result = main.build_cpp_modules()
        assert result is False
    
    @patch('os.path.exists')
    def test_build_cpp_modules_no_cmake(self, mock_exists):
        """Test C++ module build without CMake"""
        mock_exists.return_value = False
        
        result = main.build_cpp_modules()
        assert result is False
    
    @patch('subprocess.run')
    @patch('os.path.exists')
    def test_build_cpp_modules_with_parallel_jobs(self, mock_exists, mock_subprocess):
        """Test C++ module build with parallel jobs"""
        mock_exists.return_value = True
        mock_subprocess.return_value.returncode = 0
        
        result = main.build_cpp_modules(parallel_jobs=8)
        assert result is True
        
        # Check that parallel jobs were used in make command
        make_call = None
        for call in mock_subprocess.call_args_list:
            if 'make' in str(call):
                make_call = call
                break
        
        assert make_call is not None
        assert '-j8' in str(make_call)


class TestDatabaseOperations:
    """Test database operations"""
    
    @pytest.mark.asyncio
    @patch('main.init_database')
    @patch('main.get_settings')
    async def test_init_database_success(self, mock_get_settings, mock_init_database):
        """Test successful database initialization"""
        mock_settings = Mock()
        mock_get_settings.return_value = mock_settings
        mock_init_database.return_value = None

        await main.init_database_wrapper()

        mock_get_settings.assert_called_once()
        mock_init_database.assert_called_once_with(mock_settings)

    @pytest.mark.asyncio
    @patch('main.init_database')
    @patch('main.get_settings')
    async def test_init_database_failure(self, mock_get_settings, mock_init_database):
        """Test database initialization failure"""
        mock_settings = Mock()
        mock_get_settings.return_value = mock_settings
        mock_init_database.side_effect = Exception("Database connection failed")

        with pytest.raises(Exception, match="Database connection failed"):
            await main.init_database_wrapper()


class TestEncryptionInitialization:
    """Test encryption initialization"""
    
    @pytest.mark.asyncio
    @patch('main.init_encryption')
    @patch('main.VaultManager')
    @patch('main.get_settings')
    async def test_init_encryption_success(self, mock_get_settings, mock_vault_manager, mock_init_encryption):
        """Test successful encryption initialization"""
        mock_settings = Mock()
        mock_get_settings.return_value = mock_settings
        mock_vault_instance = Mock()
        mock_vault_manager.return_value = mock_vault_instance
        mock_init_encryption.return_value = None

        await main.init_encryption_wrapper()

        mock_get_settings.assert_called_once()
        mock_vault_manager.assert_called_once_with(mock_settings)
        mock_init_encryption.assert_called_once_with(mock_vault_instance)

    @pytest.mark.asyncio
    @patch('main.VaultManager')
    @patch('main.get_settings')
    async def test_init_encryption_failure(self, mock_get_settings, mock_vault_manager):
        """Test encryption initialization failure"""
        mock_settings = Mock()
        mock_get_settings.return_value = mock_settings
        mock_vault_manager.side_effect = Exception("Vault connection failed")

        with pytest.raises(Exception, match="Vault connection failed"):
            await main.init_encryption_wrapper()


class TestApplicationStartup:
    """Test application startup sequence"""
    
    @patch('main.setup_logging')
    def test_startup_sequence(self, mock_logging):
        """Test complete application startup sequence"""
        # This would be called during app startup
        main.setup_logging()

        mock_logging.assert_called_once()
    
    def test_setup_logging(self):
        """Test logging setup"""
        main.setup_logging()
        
        # Check that logging is configured
        import logging
        logger = logging.getLogger("accounting")
        assert logger.level <= logging.INFO
    
    def test_setup_logging_debug_mode(self):
        """Test logging setup in debug mode"""
        main.setup_logging(debug=True)
        
        # Check that debug logging is enabled
        import logging
        root_logger = logging.getLogger()
        assert root_logger.level <= logging.DEBUG


class TestUtilityFunctions:
    """Test utility functions"""
    
    def test_get_version(self):
        """Test version retrieval"""
        version = main.get_version()
        assert version == "1.0.0"
    
    @patch('psutil.cpu_count')
    @patch('psutil.virtual_memory')
    @patch('psutil.disk_usage')
    def test_get_system_info(self, mock_disk, mock_memory, mock_cpu):
        """Test system information retrieval"""
        mock_cpu.return_value = 8
        mock_memory.return_value.total = 16 * 1024 * 1024 * 1024  # 16GB
        mock_disk.return_value.total = 1024 * 1024 * 1024 * 1024  # 1TB
        
        info = main.get_system_info()
        
        assert "cpu_count" in info
        assert "memory_total" in info
        assert "disk_total" in info
        assert info["cpu_count"] == 8
    
    def test_validate_environment(self):
        """Test environment validation"""
        # This should not raise an exception in test environment
        main.validate_environment()
    
    @patch('os.path.exists')
    def test_validate_environment_missing_files(self, mock_exists):
        """Test environment validation with missing files"""
        mock_exists.return_value = False
        
        with pytest.raises(SystemExit):
            main.validate_environment()


class TestErrorHandling:
    """Test error handling in main application"""
    
    @patch('main.init_database_wrapper')
    def test_database_init_error_handling(self, mock_init_db):
        """Test database initialization error handling"""
        mock_init_db.side_effect = Exception("Database error")

        runner = CliRunner()
        result = runner.invoke(main.cli, ['init-db'])
        assert result.exit_code == 1
        assert "Database error" in result.output

    @patch('uvicorn.run')
    @patch('main.init_database_wrapper')
    def test_serve_error_handling(self, mock_init_db, mock_uvicorn):
        """Test serve command error handling"""
        mock_init_db.side_effect = Exception("Initialization failed")

        runner = CliRunner()
        result = runner.invoke(main.cli, ['serve'])
        assert result.exit_code == 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
