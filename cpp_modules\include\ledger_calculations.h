/**
 * Ledger Calculations Header
 * High-performance ledger and accounting calculations
 */

#ifndef LEDGER_CALCULATIONS_H
#define LEDGER_CALCULATIONS_H

#include <string>
#include <map>
#include <vector>
#include <memory>
#include <chrono>

class LedgerCalculations {
public:
    LedgerCalculations();
    ~LedgerCalculations();
    
    /**
     * Calculate account balance with transaction history
     */
    double calculate_account_balance(
        int account_id,
        const std::vector<std::map<std::string, double>>& transactions,
        double as_of_timestamp = 0.0
    );
    
    /**
     * Process journal entry and update balances
     */
    std::map<std::string, double> process_journal_entry(
        const std::map<std::string, std::vector<std::map<std::string, double>>>& entry_lines
    );
    
    /**
     * Calculate trial balance
     */
    std::map<int, double> calculate_trial_balance(
        const std::map<int, std::vector<std::map<std::string, double>>>& account_transactions
    );
    
    /**
     * Calculate financial ratios
     */
    std::map<std::string, double> calculate_financial_ratios(
        const std::map<std::string, double>& balance_sheet_data,
        const std::map<std::string, double>& income_statement_data
    );
    
    /**
     * Calculate depreciation
     */
    double calculate_straight_line_depreciation(
        double cost,
        double salvage_value,
        int useful_life_years
    );
    
    /**
     * Calculate double declining balance depreciation
     */
    double calculate_double_declining_depreciation(
        double cost,
        double accumulated_depreciation,
        int useful_life_years
    );
    
    /**
     * Calculate compound interest
     */
    double calculate_compound_interest(
        double principal,
        double annual_rate,
        int periods,
        int compounds_per_period = 1
    );
    
    /**
     * Calculate present value
     */
    double calculate_present_value(
        double future_value,
        double discount_rate,
        int periods
    );
    
    /**
     * Calculate net present value
     */
    double calculate_npv(
        const std::vector<double>& cash_flows,
        double discount_rate
    );
    
    /**
     * Calculate internal rate of return
     */
    double calculate_irr(
        const std::vector<double>& cash_flows,
        double initial_guess = 0.1,
        int max_iterations = 100,
        double tolerance = 1e-6
    );
    
    /**
     * Validate journal entry balance
     */
    bool validate_journal_entry_balance(
        const std::vector<std::map<std::string, double>>& journal_lines
    );
    
    /**
     * Calculate account aging
     */
    std::map<std::string, double> calculate_account_aging(
        const std::vector<std::map<std::string, double>>& transactions,
        double current_timestamp
    );

private:
    /**
     * Helper functions
     */
    double round_to_precision(double value, int decimal_places = 2);
    bool is_debit_account_type(const std::string& account_type);
    double apply_account_type_rules(double amount, const std::string& account_type, bool is_debit);
    
    // Configuration
    double precision_tolerance_;
    std::map<std::string, bool> debit_account_types_;
};

#endif // LEDGER_CALCULATIONS_H
