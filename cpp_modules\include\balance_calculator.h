/**
 * Balance Calculator Header
 * High-performance account balance calculations
 */

#ifndef BALANCE_CALCULATOR_H
#define BALANCE_CALCULATOR_H

#include <string>
#include <map>
#include <vector>
#include <memory>
#include <mutex>
#include <unordered_map>

class BalanceCalculator {
public:
    BalanceCalculator();
    ~BalanceCalculator();
    
    /**
     * Calculate account balance as of specific date
     */
    double calculate_balance(int account_id, double as_of_timestamp = 0.0);
    
    /**
     * Calculate balance for multiple accounts
     */
    std::map<int, double> calculate_multiple_balances(
        const std::vector<int>& account_ids,
        double as_of_timestamp = 0.0
    );
    
    /**
     * Update account balance with new transaction
     */
    double update_balance(
        int account_id,
        double amount,
        bool is_debit,
        const std::string& account_type = "asset"
    );
    
    /**
     * Get cached balance
     */
    double get_cached_balance(int account_id);
    
    /**
     * Clear balance cache
     */
    void clear_cache();
    
    /**
     * Clear cache for specific account
     */
    void clear_account_cache(int account_id);
    
    /**
     * Calculate running balance for transaction history
     */
    std::vector<double> calculate_running_balances(
        const std::vector<std::map<std::string, double>>& transactions,
        double starting_balance = 0.0
    );
    
    /**
     * Calculate balance change over period
     */
    double calculate_balance_change(
        int account_id,
        double start_timestamp,
        double end_timestamp
    );
    
    /**
     * Calculate average balance over period
     */
    double calculate_average_balance(
        int account_id,
        double start_timestamp,
        double end_timestamp,
        int sample_points = 30
    );
    
    /**
     * Validate balance calculation
     */
    bool validate_balance(
        int account_id,
        double expected_balance,
        double tolerance = 0.01
    );
    
    /**
     * Get balance statistics
     */
    std::map<std::string, double> get_balance_statistics(int account_id);
    
    /**
     * Set account type for balance calculation rules
     */
    void set_account_type(int account_id, const std::string& account_type);
    
    /**
     * Bulk balance update for performance
     */
    std::map<int, double> bulk_balance_update(
        const std::map<int, std::vector<std::map<std::string, double>>>& account_transactions
    );

private:
    /**
     * Internal calculation methods
     */
    double calculate_balance_internal(
        int account_id,
        const std::vector<std::map<std::string, double>>& transactions,
        double as_of_timestamp
    );
    
    bool is_debit_normal_account(const std::string& account_type);
    double apply_balance_rules(double amount, bool is_debit, const std::string& account_type);
    
    /**
     * Cache management
     */
    void update_cache(int account_id, double balance);
    bool is_cache_valid(int account_id);
    void invalidate_cache(int account_id);
    
    // Thread-safe balance cache
    mutable std::mutex cache_mutex_;
    std::unordered_map<int, double> balance_cache_;
    std::unordered_map<int, std::chrono::system_clock::time_point> cache_timestamps_;
    std::unordered_map<int, std::string> account_types_;
    
    // Configuration
    std::chrono::minutes cache_expiry_minutes_;
    double calculation_tolerance_;
    bool enable_caching_;
};

#endif // BALANCE_CALCULATOR_H
