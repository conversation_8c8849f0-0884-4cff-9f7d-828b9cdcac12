"""
Vendor model for accounts payable
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.sql import func

from ..utils.db import Base


class Vendor(Base):
    """Vendor model"""
    
    __tablename__ = "vendors"
    
    id = Column(Integer, primary_key=True, index=True)
    vendor_id = Column(String(20), unique=True, nullable=False, index=True)
    
    # Basic information
    name = Column(String(255), nullable=False)
    email = Column(String(255))
    phone = Column(String(20))
    
    # Address
    address_line1 = Column(String(255))
    address_line2 = Column(String(255))
    city = Column(String(100))
    state = Column(String(50))
    postal_code = Column(String(20))
    country = Column(String(50), default="US")
    
    # Business information
    tax_id = Column(String(50))
    payment_terms = Column(String(50), default="NET30")
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100))
    
    def __repr__(self):
        return f"<Vendor(id='{self.vendor_id}', name='{self.name}')>"
