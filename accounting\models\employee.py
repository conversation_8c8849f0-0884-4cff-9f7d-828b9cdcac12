"""
Employee model for payroll management
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Date
from sqlalchemy.sql import func

from ..utils.db import Base


class Employee(Base):
    """Employee model"""
    
    __tablename__ = "employees"
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(String(20), unique=True, nullable=False, index=True)
    
    # Personal information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    phone = Column(String(20))
    
    # Employment information
    hire_date = Column(Date, nullable=False)
    termination_date = Column(Date)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Job information
    job_title = Column(String(100))
    department = Column(String(100))
    manager_id = Column(Integer)
    
    # Compensation (encrypted)
    base_salary = Column(Text, nullable=False)  # Encrypted annual salary
    hourly_rate = Column(Text)  # Encrypted hourly rate
    
    # Tax information
    filing_status = Column(String(20), default="single")
    allowances = Column(Integer, default=0)
    additional_withholding = Column(Text)  # Encrypted additional tax withholding
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100))
    
    def __repr__(self):
        return f"<Employee(id='{self.employee_id}', name='{self.first_name} {self.last_name}')>"
