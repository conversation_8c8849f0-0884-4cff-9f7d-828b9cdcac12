"""
Tests for business services
"""

import pytest
from decimal import Decimal
from datetime import datetime
from unittest.mock import Mo<PERSON>, AsyncMock, patch

from accounting.services.auth import AuthService
from accounting.services.ledger import LedgerService
from accounting.services.payroll import PayrollService
from accounting.services.report import ReportService


class TestAuthService:
    """Test authentication service"""
    
    @pytest.mark.asyncio
    async def test_local_authentication_success(self, auth_service):
        """Test successful local authentication"""
        token = await auth_service.authenticate("admin", "admin")
        assert token is not None
        assert isinstance(token, str)
    
    @pytest.mark.asyncio
    async def test_local_authentication_failure(self, auth_service):
        """Test failed local authentication"""
        with pytest.raises(ValueError, match="Invalid credentials"):
            await auth_service.authenticate("invalid", "invalid")
    
    @pytest.mark.asyncio
    async def test_token_verification(self, auth_service):
        """Test JWT token verification"""
        # Create a token
        token = await auth_service.authenticate("admin", "admin")
        
        # Verify the token
        payload = await auth_service.verify_token(token)
        assert payload["sub"] == "admin"
        assert "roles" in payload
        assert "permissions" in payload
    
    @pytest.mark.asyncio
    async def test_token_refresh(self, auth_service):
        """Test JWT token refresh"""
        import asyncio

        # Create a token
        original_token = await auth_service.authenticate("admin", "admin")

        # Wait a full second to ensure different timestamps
        await asyncio.sleep(1.1)

        # Refresh the token
        new_token = await auth_service.refresh_token(original_token)
        assert new_token != original_token

        # Verify new token
        payload = await auth_service.verify_token(new_token)
        assert payload["sub"] == "admin"
    
    def test_password_hashing(self, auth_service):
        """Test password hashing and verification"""
        password = "test_password_123"
        hashed = auth_service.hash_password(password)
        
        assert hashed != password
        assert auth_service.verify_password(password, hashed)
        assert not auth_service.verify_password("wrong_password", hashed)
    
    @pytest.mark.asyncio
    async def test_check_permission(self, auth_service):
        """Test permission checking"""
        token = await auth_service.authenticate("admin", "admin")
        
        # Admin should have all permissions
        assert await auth_service.check_permission(token, "read")
        assert await auth_service.check_permission(token, "write")
        assert await auth_service.check_permission(token, "admin")
    
    @pytest.mark.asyncio
    async def test_check_role(self, auth_service):
        """Test role checking"""
        token = await auth_service.authenticate("admin", "admin")
        
        # Admin should have admin role
        assert await auth_service.check_role(token, "admin")
        assert await auth_service.check_role(token, "accountant")


class TestLedgerService:
    """Test ledger service"""
    
    @pytest.mark.asyncio
    async def test_journal_entry_validation_success(self, ledger_service, sample_journal_entry):
        """Test successful journal entry validation"""
        # Should not raise an exception
        await ledger_service._validate_journal_entry(sample_journal_entry)
    
    @pytest.mark.asyncio
    async def test_journal_entry_validation_missing_fields(self, ledger_service):
        """Test journal entry validation with missing fields"""
        invalid_entry = {
            "description": "Test entry"
            # Missing entry_date and lines
        }
        
        with pytest.raises(ValueError, match="Missing required field"):
            await ledger_service._validate_journal_entry(invalid_entry)
    
    @pytest.mark.asyncio
    async def test_journal_entry_validation_empty_lines(self, ledger_service):
        """Test journal entry validation with empty lines"""
        invalid_entry = {
            "description": "Test entry",
            "entry_date": "2023-12-01T00:00:00",
            "lines": []
        }
        
        with pytest.raises(ValueError, match="must have at least one line"):
            await ledger_service._validate_journal_entry(invalid_entry)
    
    @pytest.mark.asyncio
    async def test_journal_entry_validation_insufficient_lines(self, ledger_service):
        """Test journal entry validation with insufficient lines"""
        invalid_entry = {
            "description": "Test entry",
            "entry_date": "2023-12-01T00:00:00",
            "lines": [
                {
                    "account_id": 1,
                    "amount": "1000.00",
                    "type": "debit"
                }
            ]
        }
        
        with pytest.raises(ValueError, match="must have at least two lines"):
            await ledger_service._validate_journal_entry(invalid_entry)
    
    @pytest.mark.asyncio
    async def test_journal_entry_validation_invalid_line_type(self, ledger_service):
        """Test journal entry validation with invalid line type"""
        invalid_entry = {
            "description": "Test entry",
            "entry_date": "2023-12-01T00:00:00",
            "lines": [
                {
                    "account_id": 1,
                    "amount": "1000.00",
                    "type": "invalid_type"
                },
                {
                    "account_id": 2,
                    "amount": "1000.00",
                    "type": "credit"
                }
            ]
        }
        
        with pytest.raises(ValueError, match="Invalid line type"):
            await ledger_service._validate_journal_entry(invalid_entry)
    
    @pytest.mark.asyncio
    async def test_journal_entry_validation_negative_amount(self, ledger_service):
        """Test journal entry validation with negative amount"""
        invalid_entry = {
            "description": "Test entry",
            "entry_date": "2023-12-01T00:00:00",
            "lines": [
                {
                    "account_id": 1,
                    "amount": "-1000.00",
                    "type": "debit"
                },
                {
                    "account_id": 2,
                    "amount": "1000.00",
                    "type": "credit"
                }
            ]
        }
        
        with pytest.raises(ValueError, match="Line amount must be positive"):
            await ledger_service._validate_journal_entry(invalid_entry)
    
    def test_cpp_engine_integration(self, ledger_service):
        """Test C++ engine integration"""
        # Test FX calculation
        result = ledger_service.ledger_engine.calculate_fx_allocation(1000.0, "USD", "EUR")
        assert result == 850.0
        
        # Test balance update
        new_balance = ledger_service.ledger_engine.update_account_balance(1, 1000.0, True)
        assert new_balance == 5000.0


class TestPayrollService:
    """Test payroll service"""
    
    @pytest.mark.asyncio
    async def test_payroll_data_validation_success(self, payroll_service, sample_payroll_data):
        """Test successful payroll data validation"""
        # Should not raise an exception
        await payroll_service._validate_payroll_data(sample_payroll_data)
    
    @pytest.mark.asyncio
    async def test_payroll_data_validation_missing_fields(self, payroll_service):
        """Test payroll data validation with missing fields"""
        invalid_data = {
            "period_start": "2023-12-01T00:00:00"
            # Missing period_end
        }
        
        with pytest.raises(ValueError, match="Missing required field"):
            await payroll_service._validate_payroll_data(invalid_data)
    
    @pytest.mark.asyncio
    async def test_payroll_data_validation_invalid_dates(self, payroll_service):
        """Test payroll data validation with invalid dates"""
        invalid_data = {
            "period_start": "invalid-date",
            "period_end": "2023-12-15T00:00:00"
        }
        
        with pytest.raises(ValueError, match="Invalid date format"):
            await payroll_service._validate_payroll_data(invalid_data)
    
    @pytest.mark.asyncio
    async def test_payroll_calculation_python(self, payroll_service):
        """Test Python payroll calculation"""
        result = await payroll_service._calculate_payroll_python(
            employee_id=1,
            base_salary=Decimal('50000'),
            regular_hours=Decimal('40'),
            overtime_hours=Decimal('5')
        )
        
        assert "gross_pay" in result
        assert "net_pay" in result
        assert "total_taxes" in result
        assert "overtime_pay" in result
        
        # Verify calculations
        assert result["gross_pay"] > 0
        assert result["net_pay"] > 0
        assert result["net_pay"] < result["gross_pay"]
        assert result["overtime_pay"] > 0
    
    @pytest.mark.asyncio
    async def test_tax_withholding_calculation(self, payroll_service):
        """Test tax withholding calculation"""
        employee_data = {
            "filing_status": "single",
            "allowances": 1,
            "state": "CA"
        }
        
        result = await payroll_service._calculate_tax_withholding_python(
            Decimal('5000'), employee_data
        )
        
        assert "federal_tax" in result
        assert "state_tax" in result
        assert "social_security" in result
        assert "medicare" in result
        
        # Verify tax calculations
        assert result["federal_tax"] >= 0
        assert result["state_tax"] >= 0
        assert result["social_security"] == Decimal('5000') * Decimal('0.062')
        assert result["medicare"] == Decimal('5000') * Decimal('0.0145')
    
    def test_cpp_engine_integration(self, payroll_service):
        """Test C++ payroll engine integration"""
        expected_result = {
            "gross_pay": 5000.0,
            "net_pay": 3500.0,
            "total_taxes": 1200.0,
            "total_deductions": 300.0,
            "overtime_pay": 500.0
        }
        
        result = payroll_service.payroll_engine.calculate_employee_payroll(
            1, 50000.0, 80.0, 10.0
        )
        
        assert result == expected_result


class TestReportService:
    """Test report service"""
    
    @pytest.mark.asyncio
    async def test_balance_sheet_structure(self, report_service, mock_db_session):
        """Test balance sheet report structure"""
        # Mock account data
        mock_accounts = []

        # Configure the mock chain properly
        mock_scalars = Mock()
        mock_scalars.all.return_value = mock_accounts
        mock_result = Mock()
        mock_result.scalars.return_value = mock_scalars

        # Make execute return a coroutine that resolves to mock_result
        async def mock_execute(*args, **kwargs):
            return mock_result
        mock_db_session.execute = mock_execute

        # Mock the balance calculation
        with patch.object(report_service, '_get_account_balance', return_value=Decimal('0')):
            result = await report_service.generate_balance_sheet(
                as_of_date="2023-12-31T00:00:00",
                db_session=mock_db_session
            )
        
        # Verify structure
        assert "assets" in result
        assert "liabilities" in result
        assert "equity" in result
        assert "report_date" in result
        
        assert "current_assets" in result["assets"]
        assert "non_current_assets" in result["assets"]
        assert "total_assets" in result["assets"]
        
        assert "current_liabilities" in result["liabilities"]
        assert "non_current_liabilities" in result["liabilities"]
        assert "total_liabilities" in result["liabilities"]
        
        assert "equity_accounts" in result["equity"]
        assert "total_equity" in result["equity"]
    
    @pytest.mark.asyncio
    async def test_income_statement_structure(self, report_service, mock_db_session):
        """Test income statement report structure"""
        # Mock account data
        mock_accounts = []

        # Configure the mock chain properly
        mock_scalars = Mock()
        mock_scalars.all.return_value = mock_accounts
        mock_result = Mock()
        mock_result.scalars.return_value = mock_scalars

        # Make execute return a coroutine that resolves to mock_result
        async def mock_execute(*args, **kwargs):
            return mock_result
        mock_db_session.execute = mock_execute

        # Mock the balance calculation
        with patch.object(report_service, '_get_account_balance_for_period', return_value=Decimal('0')):
            result = await report_service.generate_income_statement(
                start_date="2023-12-01T00:00:00",
                end_date="2023-12-31T00:00:00",
                db_session=mock_db_session
            )
        
        # Verify structure
        assert "revenue" in result
        assert "expenses" in result
        assert "net_income" in result
        assert "period_start" in result
        assert "period_end" in result
        
        assert "revenue_accounts" in result["revenue"]
        assert "total_revenue" in result["revenue"]
        
        assert "operating_expenses" in result["expenses"]
        assert "non_operating_expenses" in result["expenses"]
        assert "total_expenses" in result["expenses"]
    
    @pytest.mark.asyncio
    async def test_cash_flow_statement_structure(self, report_service, mock_db_session):
        """Test cash flow statement report structure"""
        # Mock account data
        mock_accounts = []

        # Configure the mock chain properly
        mock_scalars = Mock()
        mock_scalars.all.return_value = mock_accounts
        mock_result = Mock()
        mock_result.scalars.return_value = mock_scalars

        # Make execute return a coroutine that resolves to mock_result
        async def mock_execute(*args, **kwargs):
            return mock_result
        mock_db_session.execute = mock_execute

        # Mock the balance calculation and income statement
        with patch.object(report_service, '_get_account_balance', return_value=Decimal('0')):
            with patch.object(report_service, 'generate_income_statement') as mock_income:
                mock_income.return_value = {"net_income": "1000.00"}

                result = await report_service.generate_cash_flow_statement(
                    start_date="2023-12-01T00:00:00",
                    end_date="2023-12-31T00:00:00",
                    db_session=mock_db_session
                )
        
        # Verify structure
        assert "operating_activities" in result
        assert "investing_activities" in result
        assert "financing_activities" in result
        assert "net_change_in_cash" in result
        assert "cash_beginning" in result
        assert "cash_ending" in result
        assert "period_start" in result
        assert "period_end" in result


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
