/**
 * Tax Calculator Implementation
 * High-performance tax calculations for payroll and transactions
 */

#include "../include/tax_calculator.h"
#include <cmath>
#include <algorithm>
#include <stdexcept>

TaxCalculator::TaxCalculator() 
    : current_tax_year_(2023)
    , social_security_rate_(0.062)
    , medicare_rate_(0.0145)
    , additional_medicare_rate_(0.009)
    , futa_rate_(0.006) {
    
    initialize_tax_tables();
    initialize_state_rates();
    initialize_sales_tax_rates();
}

TaxCalculator::~TaxCalculator() = default;

std::map<std::string, double> TaxCalculator::calculate_withholding(
    double gross_pay,
    const std::string& filing_status,
    int allowances,
    const std::string& state
) {
    std::map<std::string, double> result;
    
    try {
        result["federal_tax"] = calculate_federal_income_tax(gross_pay, filing_status, allowances);
        result["state_tax"] = calculate_state_income_tax(gross_pay, state, filing_status);
        result["social_security"] = calculate_social_security_tax(gross_pay);
        result["medicare"] = calculate_medicare_tax(gross_pay, 0.0, filing_status);
        
        // Calculate total
        result["total_withholding"] = result["federal_tax"] + result["state_tax"] + 
                                    result["social_security"] + result["medicare"];
        
    } catch (const std::exception& e) {
        // Return zero values on error
        result["federal_tax"] = 0.0;
        result["state_tax"] = 0.0;
        result["social_security"] = 0.0;
        result["medicare"] = 0.0;
        result["total_withholding"] = 0.0;
    }
    
    return result;
}

double TaxCalculator::calculate_federal_income_tax(
    double gross_pay,
    const std::string& filing_status,
    int allowances,
    double additional_withholding
) {
    // Get tax brackets for filing status
    auto brackets = get_tax_brackets(filing_status);
    
    // Calculate allowance deduction
    double allowance_amount = 4300.0 * allowances; // 2023 allowance amount
    double taxable_income = std::max(0.0, gross_pay - allowance_amount);
    
    // Calculate tax from brackets
    double tax = calculate_tax_from_brackets(taxable_income, brackets);
    
    return tax + additional_withholding;
}

double TaxCalculator::calculate_state_income_tax(
    double gross_pay,
    const std::string& state,
    const std::string& filing_status
) {
    double rate = get_state_tax_rate(state);
    return gross_pay * rate;
}

double TaxCalculator::calculate_social_security_tax(
    double gross_pay,
    double ytd_gross,
    double wage_base
) {
    if (ytd_gross >= wage_base) {
        return 0.0; // Already hit the wage base
    }
    
    double taxable_amount = std::min(gross_pay, wage_base - ytd_gross);
    return taxable_amount * social_security_rate_;
}

double TaxCalculator::calculate_medicare_tax(
    double gross_pay,
    double ytd_gross,
    const std::string& filing_status
) {
    double regular_medicare = gross_pay * medicare_rate_;
    
    // Additional Medicare tax thresholds
    double threshold = 200000.0; // Single
    if (filing_status == "married_joint") {
        threshold = 250000.0;
    } else if (filing_status == "married_separate") {
        threshold = 125000.0;
    }
    
    double additional_medicare = 0.0;
    if (ytd_gross + gross_pay > threshold) {
        double excess = std::max(0.0, ytd_gross + gross_pay - threshold);
        additional_medicare = std::min(excess, gross_pay) * additional_medicare_rate_;
    }
    
    return regular_medicare + additional_medicare;
}

double TaxCalculator::calculate_futa_tax(
    double gross_pay,
    double ytd_gross,
    double wage_base,
    double rate
) {
    if (ytd_gross >= wage_base) {
        return 0.0;
    }
    
    double taxable_amount = std::min(gross_pay, wage_base - ytd_gross);
    return taxable_amount * rate;
}

double TaxCalculator::calculate_suta_tax(
    double gross_pay,
    const std::string& state,
    double ytd_gross,
    double experience_rate
) {
    double rate = get_suta_rate(state, experience_rate);
    
    // Most states have wage bases, using a common one
    double wage_base = 15000.0; // Varies by state
    
    if (ytd_gross >= wage_base) {
        return 0.0;
    }
    
    double taxable_amount = std::min(gross_pay, wage_base - ytd_gross);
    return taxable_amount * rate;
}

double TaxCalculator::calculate_sales_tax(
    double amount,
    const std::string& state,
    const std::string& county,
    const std::string& city
) {
    double rate = get_sales_tax_rate(state, county, city);
    return amount * rate;
}

double TaxCalculator::calculate_property_tax(
    double assessed_value,
    double mill_rate,
    double exemption_amount
) {
    double taxable_value = std::max(0.0, assessed_value - exemption_amount);
    return (taxable_value / 1000.0) * mill_rate;
}

std::vector<std::map<std::string, double>> TaxCalculator::get_tax_brackets(
    const std::string& filing_status,
    int tax_year
) {
    auto it = federal_tax_brackets_.find(filing_status);
    if (it != federal_tax_brackets_.end()) {
        return it->second;
    }
    
    // Return default single brackets if not found
    return federal_tax_brackets_["single"];
}

double TaxCalculator::calculate_effective_tax_rate(
    double total_tax,
    double total_income
) {
    if (total_income <= 0) {
        return 0.0;
    }
    return total_tax / total_income;
}

double TaxCalculator::calculate_marginal_tax_rate(
    double income,
    const std::string& filing_status
) {
    auto brackets = get_tax_brackets(filing_status);
    
    for (const auto& bracket : brackets) {
        if (income <= bracket.at("upper_limit")) {
            return bracket.at("rate");
        }
    }
    
    // Return highest bracket rate if income exceeds all brackets
    return brackets.back().at("rate");
}

double TaxCalculator::calculate_tax_from_brackets(
    double income,
    const std::vector<std::map<std::string, double>>& brackets
) {
    double tax = 0.0;
    double previous_limit = 0.0;
    
    for (const auto& bracket : brackets) {
        double upper_limit = bracket.at("upper_limit");
        double rate = bracket.at("rate");
        
        if (income <= previous_limit) {
            break;
        }
        
        double taxable_in_bracket = std::min(income, upper_limit) - previous_limit;
        tax += taxable_in_bracket * rate;
        
        previous_limit = upper_limit;
        
        if (income <= upper_limit) {
            break;
        }
    }
    
    return tax;
}

void TaxCalculator::initialize_tax_tables() {
    // 2023 Federal tax brackets for single filers
    federal_tax_brackets_["single"] = {
        {{"upper_limit", 11000.0}, {"rate", 0.10}},
        {{"upper_limit", 44725.0}, {"rate", 0.12}},
        {{"upper_limit", 95375.0}, {"rate", 0.22}},
        {{"upper_limit", 182050.0}, {"rate", 0.24}},
        {{"upper_limit", 231250.0}, {"rate", 0.32}},
        {{"upper_limit", 578125.0}, {"rate", 0.35}},
        {{"upper_limit", *********.0}, {"rate", 0.37}}
    };
    
    // 2023 Federal tax brackets for married filing jointly
    federal_tax_brackets_["married_joint"] = {
        {{"upper_limit", 22000.0}, {"rate", 0.10}},
        {{"upper_limit", 89450.0}, {"rate", 0.12}},
        {{"upper_limit", 190750.0}, {"rate", 0.22}},
        {{"upper_limit", 364200.0}, {"rate", 0.24}},
        {{"upper_limit", 462500.0}, {"rate", 0.32}},
        {{"upper_limit", 693750.0}, {"rate", 0.35}},
        {{"upper_limit", *********.0}, {"rate", 0.37}}
    };
}

void TaxCalculator::initialize_state_rates() {
    // Simplified state tax rates (flat rates for demonstration)
    state_tax_rates_ = {
        {"AL", 0.05}, {"AK", 0.0}, {"AZ", 0.045}, {"AR", 0.06},
        {"CA", 0.08}, {"CO", 0.045}, {"CT", 0.06}, {"DE", 0.055},
        {"FL", 0.0}, {"GA", 0.055}, {"HI", 0.08}, {"ID", 0.06},
        {"IL", 0.045}, {"IN", 0.032}, {"IA", 0.06}, {"KS", 0.055},
        {"KY", 0.05}, {"LA", 0.04}, {"ME", 0.07}, {"MD", 0.055},
        {"MA", 0.05}, {"MI", 0.042}, {"MN", 0.07}, {"MS", 0.05},
        {"MO", 0.055}, {"MT", 0.065}, {"NE", 0.065}, {"NV", 0.0},
        {"NH", 0.0}, {"NJ", 0.065}, {"NM", 0.055}, {"NY", 0.065},
        {"NC", 0.055}, {"ND", 0.03}, {"OH", 0.04}, {"OK", 0.05},
        {"OR", 0.09}, {"PA", 0.031}, {"RI", 0.055}, {"SC", 0.06},
        {"SD", 0.0}, {"TN", 0.0}, {"TX", 0.0}, {"UT", 0.05},
        {"VT", 0.065}, {"VA", 0.055}, {"WA", 0.0}, {"WV", 0.055},
        {"WI", 0.065}, {"WY", 0.0}
    };
}

void TaxCalculator::initialize_sales_tax_rates() {
    // Simplified sales tax rates by state
    sales_tax_rates_ = {
        {"AL", 0.04}, {"AK", 0.0}, {"AZ", 0.056}, {"AR", 0.065},
        {"CA", 0.075}, {"CO", 0.029}, {"CT", 0.0635}, {"DE", 0.0},
        {"FL", 0.06}, {"GA", 0.04}, {"HI", 0.04}, {"ID", 0.06},
        {"IL", 0.0625}, {"IN", 0.07}, {"IA", 0.06}, {"KS", 0.065},
        {"KY", 0.06}, {"LA", 0.045}, {"ME", 0.055}, {"MD", 0.06},
        {"MA", 0.0625}, {"MI", 0.06}, {"MN", 0.06875}, {"MS", 0.07},
        {"MO", 0.04225}, {"MT", 0.0}, {"NE", 0.055}, {"NV", 0.0685},
        {"NH", 0.0}, {"NJ", 0.06625}, {"NM", 0.05125}, {"NY", 0.08},
        {"NC", 0.0475}, {"ND", 0.05}, {"OH", 0.0575}, {"OK", 0.045},
        {"OR", 0.0}, {"PA", 0.06}, {"RI", 0.07}, {"SC", 0.06},
        {"SD", 0.045}, {"TN", 0.07}, {"TX", 0.0625}, {"UT", 0.0485},
        {"VT", 0.06}, {"VA", 0.053}, {"WA", 0.065}, {"WV", 0.06},
        {"WI", 0.05}, {"WY", 0.04}
    };
}

double TaxCalculator::get_state_tax_rate(const std::string& state) {
    auto it = state_tax_rates_.find(state);
    return (it != state_tax_rates_.end()) ? it->second : 0.05; // Default 5%
}

double TaxCalculator::get_sales_tax_rate(const std::string& state, const std::string& county, const std::string& city) {
    auto it = sales_tax_rates_.find(state);
    double base_rate = (it != sales_tax_rates_.end()) ? it->second : 0.05;
    
    // In a real implementation, you would look up county and city rates
    // For now, just return the state rate
    return base_rate;
}

double TaxCalculator::get_suta_rate(const std::string& state, double experience_rate) {
    // Simplified SUTA rates - in reality these vary significantly by state and employer
    double base_rate = 0.027; // 2.7% average
    return base_rate + experience_rate;
}
