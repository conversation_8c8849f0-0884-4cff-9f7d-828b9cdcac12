/**
 * Electron Main Process
 * Manages the Python backend and application lifecycle
 */

const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const log = require('electron-log');
const { autoUpdater } = require('electron-updater');
const Store = require('electron-store');

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

// Application state
let mainWindow = null;
let pythonProcess = null;
let pythonPort = 8000;
let isQuitting = false;

// Configuration store
const store = new Store({
    defaults: {
        windowBounds: { width: 1200, height: 800 },
        pythonPort: 8000,
        autoStart: true,
        theme: 'light'
    }
});

/**
 * Create the main application window
 */
function createMainWindow() {
    const windowBounds = store.get('windowBounds');
    
    mainWindow = new BrowserWindow({
        width: windowBounds.width,
        height: windowBounds.height,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.png'),
        show: false,
        titleBarStyle: 'default'
    });

    // Load the application
    if (app.isPackaged) {
        // Production: load from local server
        mainWindow.loadURL(`http://localhost:${pythonPort}`);
    } else {
        // Development: load from file or dev server
        const indexPath = path.join(__dirname, 'index.html');
        if (fs.existsSync(indexPath)) {
            mainWindow.loadFile(indexPath);
        } else {
            mainWindow.loadURL(`http://localhost:${pythonPort}`);
        }
    }

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        if (!app.isPackaged) {
            mainWindow.webContents.openDevTools();
        }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Save window bounds on resize/move
    mainWindow.on('resize', () => {
        store.set('windowBounds', mainWindow.getBounds());
    });

    mainWindow.on('move', () => {
        store.set('windowBounds', mainWindow.getBounds());
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    return mainWindow;
}

/**
 * Start the Python backend process
 */
function startPythonBackend() {
    return new Promise((resolve, reject) => {
        log.info('Starting Python backend...');
        
        let pythonCmd = 'python';
        let scriptPath = 'main.py';
        
        if (app.isPackaged) {
            // In packaged app, use bundled Python and script
            const resourcesPath = process.resourcesPath;
            const pythonBackendPath = path.join(resourcesPath, 'python-backend');
            scriptPath = path.join(pythonBackendPath, 'main.py');
            
            // Try different Python commands
            const pythonCommands = ['python', 'python3', 'py'];
            pythonCmd = pythonCommands[0]; // Default to first
        }

        const args = ['serve', '--host', '127.0.0.1', '--port', pythonPort.toString()];
        
        log.info(`Executing: ${pythonCmd} ${scriptPath} ${args.join(' ')}`);
        
        pythonProcess = spawn(pythonCmd, [scriptPath, ...args], {
            cwd: app.isPackaged ? path.dirname(scriptPath) : process.cwd(),
            stdio: ['pipe', 'pipe', 'pipe']
        });

        pythonProcess.stdout.on('data', (data) => {
            log.info(`Python stdout: ${data.toString()}`);
        });

        pythonProcess.stderr.on('data', (data) => {
            log.error(`Python stderr: ${data.toString()}`);
        });

        pythonProcess.on('error', (error) => {
            log.error(`Python process error: ${error.message}`);
            reject(error);
        });

        pythonProcess.on('exit', (code, signal) => {
            log.info(`Python process exited with code ${code} and signal ${signal}`);
            if (!isQuitting) {
                // Restart if not intentionally quitting
                setTimeout(() => {
                    startPythonBackend().catch(log.error);
                }, 5000);
            }
        });

        // Wait for server to start
        setTimeout(() => {
            checkServerHealth()
                .then(() => {
                    log.info('Python backend started successfully');
                    resolve();
                })
                .catch((error) => {
                    log.error('Python backend health check failed:', error);
                    reject(error);
                });
        }, 3000);
    });
}

/**
 * Check if Python backend is healthy
 */
async function checkServerHealth() {
    const http = require('http');
    
    return new Promise((resolve, reject) => {
        const req = http.get(`http://localhost:${pythonPort}/health`, (res) => {
            if (res.statusCode === 200) {
                resolve();
            } else {
                reject(new Error(`Health check failed with status ${res.statusCode}`));
            }
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('Health check timeout'));
        });
    });
}

/**
 * Stop the Python backend process
 */
function stopPythonBackend() {
    if (pythonProcess) {
        log.info('Stopping Python backend...');
        isQuitting = true;
        
        if (process.platform === 'win32') {
            spawn('taskkill', ['/pid', pythonProcess.pid, '/f', '/t']);
        } else {
            pythonProcess.kill('SIGTERM');
        }
        
        pythonProcess = null;
    }
}

/**
 * Create application menu
 */
function createMenu() {
    const template = [
        {
            label: 'File',
            submenu: [
                {
                    label: 'New Journal Entry',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('menu-action', 'new-journal-entry');
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Import Data',
                    click: async () => {
                        const result = await dialog.showOpenDialog(mainWindow, {
                            properties: ['openFile'],
                            filters: [
                                { name: 'CSV Files', extensions: ['csv'] },
                                { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
                                { name: 'All Files', extensions: ['*'] }
                            ]
                        });
                        
                        if (!result.canceled && result.filePaths.length > 0) {
                            mainWindow.webContents.send('menu-action', 'import-data', result.filePaths[0]);
                        }
                    }
                },
                {
                    label: 'Export Data',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('menu-action', 'export-data');
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Exit',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Reports',
            submenu: [
                {
                    label: 'Balance Sheet',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('menu-action', 'balance-sheet');
                        }
                    }
                },
                {
                    label: 'Income Statement',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('menu-action', 'income-statement');
                        }
                    }
                },
                {
                    label: 'Cash Flow Statement',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('menu-action', 'cash-flow');
                        }
                    }
                }
            ]
        },
        {
            label: 'Tools',
            submenu: [
                {
                    label: 'Settings',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('menu-action', 'settings');
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Restart Backend',
                    click: async () => {
                        stopPythonBackend();
                        try {
                            await startPythonBackend();
                            if (mainWindow) {
                                mainWindow.reload();
                            }
                        } catch (error) {
                            log.error('Failed to restart backend:', error);
                        }
                    }
                }
            ]
        },
        {
            label: 'Help',
            submenu: [
                {
                    label: 'About',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'About Enterprise Accounting',
                            message: 'Enterprise Accounting & Financial Management System',
                            detail: 'Version 1.0.0\nBuilt with Python, C++, and Electron.js'
                        });
                    }
                },
                {
                    label: 'Documentation',
                    click: () => {
                        shell.openExternal('https://docs.enterprise-accounting.com');
                    }
                }
            ]
        }
    ];

    // macOS specific menu adjustments
    if (process.platform === 'darwin') {
        template.unshift({
            label: app.getName(),
            submenu: [
                { role: 'about' },
                { type: 'separator' },
                { role: 'services' },
                { type: 'separator' },
                { role: 'hide' },
                { role: 'hideothers' },
                { role: 'unhide' },
                { type: 'separator' },
                { role: 'quit' }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
    log.info('App ready, initializing...');
    
    try {
        // Start Python backend first
        await startPythonBackend();
        
        // Create main window
        createMainWindow();
        
        // Create menu
        createMenu();
        
        // Setup auto-updater
        autoUpdater.checkForUpdatesAndNotify();
        
    } catch (error) {
        log.error('Failed to initialize app:', error);
        
        dialog.showErrorBox(
            'Startup Error',
            'Failed to start the application backend. Please check the logs for more details.'
        );
        
        app.quit();
    }
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
    }
});

app.on('before-quit', () => {
    isQuitting = true;
    stopPythonBackend();
});

// IPC handlers
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('get-python-port', () => {
    return pythonPort;
});

ipcMain.handle('restart-backend', async () => {
    stopPythonBackend();
    await startPythonBackend();
    return true;
});

// Auto-updater events
autoUpdater.on('checking-for-update', () => {
    log.info('Checking for update...');
});

autoUpdater.on('update-available', (info) => {
    log.info('Update available:', info);
});

autoUpdater.on('update-not-available', (info) => {
    log.info('Update not available:', info);
});

autoUpdater.on('error', (err) => {
    log.error('Error in auto-updater:', err);
});

autoUpdater.on('download-progress', (progressObj) => {
    let log_message = "Download speed: " + progressObj.bytesPerSecond;
    log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
    log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
    log.info(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
    log.info('Update downloaded:', info);
    autoUpdater.quitAndInstall();
});
