version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: accounting-postgres
    environment:
      POSTGRES_DB: accounting
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - accounting-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Celery
  redis:
    image: redis:7-alpine
    container_name: accounting-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - accounting-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: accounting-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - accounting-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # HashiCorp Vault
  vault:
    image: vault:1.15
    container_name: accounting-vault
    cap_add:
      - IPC_LOCK
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: myroot
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    ports:
      - "8200:8200"
    volumes:
      - vault_data:/vault/data
      - ./vault/config:/vault/config
    networks:
      - accounting-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 5
    command: ["vault", "server", "-dev"]

  # Main Application
  accounting-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: accounting-app
    environment:
      DATABASE_URL: ********************************************/accounting
      VAULT_URL: http://vault:8200
      VAULT_TOKEN: myroot
      RABBITMQ_URL: amqp://guest:guest@rabbitmq:5672//
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      ENVIRONMENT: development
      DEBUG: "true"
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - accounting-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      vault:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: accounting-celery-worker
    environment:
      DATABASE_URL: ********************************************/accounting
      VAULT_URL: http://vault:8200
      VAULT_TOKEN: myroot
      RABBITMQ_URL: amqp://guest:guest@rabbitmq:5672//
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      ENVIRONMENT: development
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - accounting-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - vault
    restart: unless-stopped
    command: ["celery", "-A", "accounting.tasks", "worker", "--loglevel=info"]

  # Celery Beat (Scheduler)
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: accounting-celery-beat
    environment:
      DATABASE_URL: ********************************************/accounting
      VAULT_URL: http://vault:8200
      VAULT_TOKEN: myroot
      RABBITMQ_URL: amqp://guest:guest@rabbitmq:5672//
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      ENVIRONMENT: development
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - accounting-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - vault
    restart: unless-stopped
    command: ["celery", "-A", "accounting.tasks", "beat", "--loglevel=info"]

  # Flower (Celery Monitoring)
  flower:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: accounting-flower
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
    ports:
      - "5555:5555"
    networks:
      - accounting-network
    depends_on:
      - redis
    restart: unless-stopped
    command: ["celery", "-A", "accounting.tasks", "flower", "--port=5555"]

  # Nginx (Optional - for production)
  nginx:
    image: nginx:alpine
    container_name: accounting-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - accounting-network
    depends_on:
      - accounting-app
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  vault_data:
    driver: local

networks:
  accounting-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
