"""
Configuration management for Enterprise Accounting System
Handles Vault, Database, RabbitMQ, and other service configurations
"""

import os
from functools import lru_cache
from typing import Optional

try:
    from pydantic_settings import BaseSettings
    from pydantic import ConfigDict
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings, ConfigDict


class Settings(BaseSettings):
    """Application settings with environment variable support"""

    model_config = ConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False)

    # Application settings
    app_name: str = "Enterprise Accounting System"
    debug: bool = False
    secret_key: str = "your-secret-key-change-in-production"
    
    # Database settings
    database_url: str = "postgresql://postgres:password@localhost:5432/accounting"
    database_echo: bool = False
    
    # HashiCorp Vault settings
    vault_url: str = "http://localhost:8200"
    vault_token: Optional[str] = None
    vault_role_id: Optional[str] = None
    vault_secret_id: Optional[str] = None
    vault_transit_key: str = "accounting-key"
    vault_mount_point: str = "transit"
    
    # RabbitMQ settings
    rabbitmq_url: str = "amqp://guest:guest@localhost:5672//"

    # Celery settings
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"
    
    # Keycloak/Auth settings
    keycloak_url: str = "http://localhost:8080"
    keycloak_realm: str = "accounting"
    keycloak_client_id: str = "accounting-client"
    keycloak_client_secret: Optional[str] = None
    
    # Encryption settings
    libsodium_key_size: int = 32
    encryption_algorithm: str = "ChaCha20Poly1305"
    
    # Logging settings
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # C++ Module settings
    cpp_build_type: str = "Release"
    cpp_parallel_jobs: int = 4

    # Financial settings
    default_currency: str = "USD"
    fiscal_year_start: str = "01-01"  # MM-DD format

    # Security settings
    jwt_secret_key: str = "jwt-secret-change-in-production"
    jwt_algorithm: str = "HS256"
    jwt_expiration_hours: int = 24

    # Rate limiting
    rate_limit_per_minute: int = 100

    # File storage
    upload_directory: str = "./uploads"
    max_file_size_mb: int = 10
    



@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()


# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings"""
    debug: bool = True
    database_echo: bool = True
    log_level: str = "DEBUG"


class ProductionSettings(Settings):
    """Production environment settings"""
    debug: bool = False
    database_echo: bool = False
    log_level: str = "WARNING"


class TestingSettings(Settings):
    """Testing environment settings"""
    debug: bool = True
    database_url: str = "sqlite:///./test.db"
    vault_url: str = "http://localhost:8200"
    vault_token: str = "test-token"


def get_settings_for_environment(env: str = None) -> Settings:
    """Get settings for specific environment"""
    if env is None:
        env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# Vault configuration helpers
def get_vault_config() -> dict:
    """Get Vault configuration dictionary"""
    settings = get_settings()
    
    config = {
        "url": settings.vault_url,
        "mount_point": settings.vault_mount_point,
        "key_name": settings.vault_transit_key,
    }
    
    # Add authentication method
    if settings.vault_token:
        config["token"] = settings.vault_token
    elif settings.vault_role_id and settings.vault_secret_id:
        config["role_id"] = settings.vault_role_id
        config["secret_id"] = settings.vault_secret_id
    
    return config


# Database configuration helpers
def get_database_config() -> dict:
    """Get database configuration dictionary"""
    settings = get_settings()
    
    return {
        "url": settings.database_url,
        "echo": settings.database_echo,
        "pool_size": 10,
        "max_overflow": 20,
        "pool_pre_ping": True,
        "pool_recycle": 3600,
    }


# Celery configuration
def get_celery_config() -> dict:
    """Get Celery configuration dictionary"""
    settings = get_settings()
    
    return {
        "broker_url": settings.celery_broker_url,
        "result_backend": settings.celery_result_backend,
        "task_serializer": "json",
        "accept_content": ["json"],
        "result_serializer": "json",
        "timezone": "UTC",
        "enable_utc": True,
        "task_track_started": True,
        "task_time_limit": 30 * 60,  # 30 minutes
        "task_soft_time_limit": 25 * 60,  # 25 minutes
        "worker_prefetch_multiplier": 1,
        "worker_max_tasks_per_child": 1000,
    }
