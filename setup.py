#!/usr/bin/env python3
"""
Setup script for Enterprise Accounting & Financial Management System
"""

from setuptools import setup, find_packages, Extension
from pybind11.setup_helpers import Pybind11Extension, build_ext
from pybind11 import get_cmake_dir
import pybind11
import os
import sys
from pathlib import Path

# Read version from __init__.py
def get_version():
    version_file = Path("accounting") / "__init__.py"
    with open(version_file, "r", encoding="utf-8") as f:
        for line in f:
            if line.startswith("__version__"):
                return line.split("=")[1].strip().strip('"').strip("'")
    return "1.0.0"

# Read long description from README
def get_long_description():
    readme_file = Path("README.md")
    if readme_file.exists():
        with open(readme_file, "r", encoding="utf-8") as f:
            return f.read()
    return "Enterprise Accounting & Financial Management System"

# Read requirements
def get_requirements():
    requirements_file = Path("requirements.txt")
    if requirements_file.exists():
        with open(requirements_file, "r", encoding="utf-8") as f:
            return [
                line.strip() 
                for line in f 
                if line.strip() and not line.startswith("#")
            ]
    return []

# C++ extensions using pybind11
ext_modules = [
    Pybind11Extension(
        "ledger_engine",
        [
            "cpp_modules/ledger_engine.cpp",
            "cpp_modules/src/ledger_calculations.cpp",
            "cpp_modules/src/fx_engine.cpp",
            "cpp_modules/src/balance_calculator.cpp",
        ],
        include_dirs=[
            "cpp_modules/include",
            pybind11.get_include(),
        ],
        language="c++",
        cxx_std=17,
    ),
    Pybind11Extension(
        "payroll_engine",
        [
            "cpp_modules/payroll_engine.cpp",
            "cpp_modules/src/payroll_calculations.cpp",
            "cpp_modules/src/tax_calculator.cpp",
            "cpp_modules/src/benefits_calculator.cpp",
        ],
        include_dirs=[
            "cpp_modules/include",
            pybind11.get_include(),
        ],
        language="c++",
        cxx_std=17,
    ),
]

# Custom build command that handles missing C++ files gracefully
class CustomBuildExt(build_ext):
    def build_extensions(self):
        # Check if C++ source files exist
        missing_files = []
        for ext in self.extensions:
            for source in ext.sources:
                if not os.path.exists(source):
                    missing_files.append(source)
        
        if missing_files:
            print(f"Warning: Missing C++ source files: {missing_files}")
            print("Skipping C++ extension build. Run 'python main.py build-modules' to build C++ modules.")
            # Remove extensions with missing files
            self.extensions = []
        else:
            super().build_extensions()

setup(
    name="enterprise-accounting",
    version=get_version(),
    author="Enterprise Accounting Team",
    author_email="<EMAIL>",
    description="High-performance accounting system with C++ engines",
    long_description=get_long_description(),
    long_description_content_type="text/markdown",
    url="https://github.com/enterprise/accounting",
    project_urls={
        "Bug Tracker": "https://github.com/enterprise/accounting/issues",
        "Documentation": "https://docs.enterprise-accounting.com",
        "Source Code": "https://github.com/enterprise/accounting",
    },
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: C++",
        "Topic :: Office/Business :: Financial :: Accounting",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.9",
    install_requires=get_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-mock>=3.12.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.7.0",
            "pre-commit>=3.0.0",
        ],
        "docs": [
            "sphinx>=7.0.0",
            "sphinx-rtd-theme>=1.3.0",
            "myst-parser>=2.0.0",
        ],
        "deployment": [
            "gunicorn>=21.0.0",
            "supervisor>=4.2.0",
            "docker>=6.0.0",
        ],
    },
    ext_modules=ext_modules,
    cmdclass={"build_ext": CustomBuildExt},
    zip_safe=False,
    include_package_data=True,
    package_data={
        "accounting": [
            "templates/*.html",
            "static/**/*",
            "migrations/*.sql",
        ],
    },
    entry_points={
        "console_scripts": [
            "accounting=main:cli",
            "accounting-server=main:serve",
            "accounting-init=main:init_db",
        ],
    },
    keywords=[
        "accounting",
        "finance",
        "erp",
        "bookkeeping",
        "financial-management",
        "enterprise",
        "cpp",
        "high-performance",
        "vault",
        "encryption",
    ],
    platforms=["any"],
    license="MIT",
    # Metadata for PyPI
    maintainer="Enterprise Accounting Team",
    maintainer_email="<EMAIL>",
    download_url="https://github.com/enterprise/accounting/archive/v1.0.0.tar.gz",
)
