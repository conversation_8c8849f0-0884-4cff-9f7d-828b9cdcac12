"""
Encryption utilities using HashiCorp Vault Transit engine and libsodium
Provides secure field-level encryption for sensitive financial data
"""

import base64
import logging
from typing import Optional, Union, Dict, Any
import asyncio

import hvac
import nacl.secret
import nacl.utils
from nacl.exceptions import CryptoError

from ..config import Settings, get_vault_config

logger = logging.getLogger(__name__)


class VaultManager:
    """HashiCorp Vault manager for encryption operations"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client: Optional[hvac.Client] = None
        self.transit_key = settings.vault_transit_key
        self.mount_point = settings.vault_mount_point
        self._authenticated = False
        
    async def initialize(self):
        """Initialize Vault client and authenticate"""
        try:
            self.client = hvac.Client(url=self.settings.vault_url)
            
            # Authenticate using token or AppRole
            if self.settings.vault_token:
                self.client.token = self.settings.vault_token
            elif self.settings.vault_role_id and self.settings.vault_secret_id:
                auth_response = self.client.auth.approle.login(
                    role_id=self.settings.vault_role_id,
                    secret_id=self.settings.vault_secret_id
                )
                self.client.token = auth_response['auth']['client_token']
            else:
                raise ValueError("No valid Vault authentication method configured")
            
            # Verify authentication
            if not self.client.is_authenticated():
                raise ValueError("Failed to authenticate with Vault")
            
            # Ensure transit engine is enabled
            await self._ensure_transit_engine()
            
            # Ensure encryption key exists
            await self._ensure_encryption_key()
            
            self._authenticated = True
            logger.info("Vault manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Vault manager: {e}")
            raise
    
    async def _ensure_transit_engine(self):
        """Ensure transit engine is enabled"""
        try:
            engines = self.client.sys.list_auth_methods()
            if f"{self.mount_point}/" not in engines:
                self.client.sys.enable_secrets_engine(
                    backend_type='transit',
                    path=self.mount_point
                )
                logger.info(f"Transit engine enabled at {self.mount_point}")
        except Exception as e:
            logger.warning(f"Could not verify/enable transit engine: {e}")
    
    async def _ensure_encryption_key(self):
        """Ensure encryption key exists in Vault"""
        try:
            # Try to read the key
            self.client.secrets.transit.read_key(
                name=self.transit_key,
                mount_point=self.mount_point
            )
        except hvac.exceptions.InvalidPath:
            # Key doesn't exist, create it
            self.client.secrets.transit.create_key(
                name=self.transit_key,
                mount_point=self.mount_point,
                key_type='chacha20-poly1305'
            )
            logger.info(f"Created encryption key: {self.transit_key}")
    
    async def encrypt(self, plaintext: str) -> str:
        """Encrypt plaintext using Vault Transit engine"""
        if not self._authenticated:
            await self.initialize()
        
        try:
            # Encode plaintext to base64
            encoded_plaintext = base64.b64encode(plaintext.encode()).decode()
            
            # Encrypt using Vault
            response = self.client.secrets.transit.encrypt_data(
                name=self.transit_key,
                mount_point=self.mount_point,
                plaintext=encoded_plaintext
            )
            
            return response['data']['ciphertext']
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    async def decrypt(self, ciphertext: str) -> str:
        """Decrypt ciphertext using Vault Transit engine"""
        if not self._authenticated:
            await self.initialize()
        
        try:
            # Decrypt using Vault
            response = self.client.secrets.transit.decrypt_data(
                name=self.transit_key,
                mount_point=self.mount_point,
                ciphertext=ciphertext
            )
            
            # Decode from base64
            decoded_plaintext = base64.b64decode(
                response['data']['plaintext']
            ).decode()
            
            return decoded_plaintext
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise
    
    async def rotate_key(self):
        """Rotate the encryption key"""
        if not self._authenticated:
            await self.initialize()
        
        try:
            self.client.secrets.transit.rotate_key(
                name=self.transit_key,
                mount_point=self.mount_point
            )
            logger.info(f"Rotated encryption key: {self.transit_key}")
        except Exception as e:
            logger.error(f"Key rotation failed: {e}")
            raise


class LibsodiumManager:
    """Local encryption using libsodium for high-performance operations"""
    
    def __init__(self, key: bytes = None):
        if key is None:
            key = nacl.utils.random(nacl.secret.SecretBox.KEY_SIZE)
        elif len(key) != nacl.secret.SecretBox.KEY_SIZE:
            raise ValueError(f"Key must be {nacl.secret.SecretBox.KEY_SIZE} bytes")
        
        self.box = nacl.secret.SecretBox(key)
        self.key = key
    
    def encrypt(self, plaintext: str) -> str:
        """Encrypt plaintext using libsodium"""
        try:
            encrypted = self.box.encrypt(plaintext.encode())
            return base64.b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Libsodium encryption failed: {e}")
            raise
    
    def decrypt(self, ciphertext: str) -> str:
        """Decrypt ciphertext using libsodium"""
        try:
            encrypted_data = base64.b64decode(ciphertext.encode())
            decrypted = self.box.decrypt(encrypted_data)
            return decrypted.decode()
        except (CryptoError, Exception) as e:
            logger.error(f"Libsodium decryption failed: {e}")
            raise
    
    @classmethod
    def generate_key(cls) -> bytes:
        """Generate a new encryption key"""
        return nacl.utils.random(nacl.secret.SecretBox.KEY_SIZE)


# Global encryption managers
_vault_manager: Optional[VaultManager] = None
_libsodium_manager: Optional[LibsodiumManager] = None


async def init_encryption(vault_manager: VaultManager):
    """Initialize encryption managers"""
    global _vault_manager, _libsodium_manager
    
    _vault_manager = vault_manager
    await _vault_manager.initialize()
    
    # Initialize libsodium with a key from Vault or generate one
    try:
        # Try to get libsodium key from Vault KV store
        key_response = _vault_manager.client.secrets.kv.v2.read_secret_version(
            path="libsodium-key"
        )
        key_data = key_response['data']['data']['key']
        key_bytes = base64.b64decode(key_data)
    except:
        # Generate new key and store in Vault
        key_bytes = LibsodiumManager.generate_key()
        key_data = base64.b64encode(key_bytes).decode()
        
        try:
            _vault_manager.client.secrets.kv.v2.create_or_update_secret(
                path="libsodium-key",
                secret={"key": key_data}
            )
        except:
            logger.warning("Could not store libsodium key in Vault")
    
    _libsodium_manager = LibsodiumManager(key_bytes)
    logger.info("Encryption managers initialized")


async def encrypt_field(value: Union[str, int, float], use_vault: bool = True) -> str:
    """Encrypt a field value"""
    if value is None:
        return None
    
    plaintext = str(value)
    
    if use_vault and _vault_manager:
        return await _vault_manager.encrypt(plaintext)
    elif _libsodium_manager:
        return _libsodium_manager.encrypt(plaintext)
    else:
        raise RuntimeError("No encryption manager available")


async def decrypt_field(ciphertext: str, use_vault: bool = True) -> str:
    """Decrypt a field value"""
    if ciphertext is None:
        return None
    
    if use_vault and _vault_manager:
        return await _vault_manager.decrypt(ciphertext)
    elif _libsodium_manager:
        return _libsodium_manager.decrypt(ciphertext)
    else:
        raise RuntimeError("No encryption manager available")


def encrypt_field_sync(value: Union[str, int, float]) -> str:
    """Synchronous field encryption using libsodium"""
    if value is None:
        return None
    
    if _libsodium_manager:
        return _libsodium_manager.encrypt(str(value))
    else:
        raise RuntimeError("Libsodium manager not available")


def decrypt_field_sync(ciphertext: str) -> str:
    """Synchronous field decryption using libsodium"""
    if ciphertext is None:
        return None
    
    if _libsodium_manager:
        return _libsodium_manager.decrypt(ciphertext)
    else:
        raise RuntimeError("Libsodium manager not available")
