"""
Payroll service with C++ engine integration
Handles payroll calculations, tax computations, and benefits processing
"""

import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, date
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..utils.encryption import VaultManager, encrypt_field, decrypt_field

logger = logging.getLogger(__name__)


class PayrollService:
    """Payroll service with high-performance C++ engine integration"""
    
    def __init__(self, vault_manager: VaultManager):
        self.vault_manager = vault_manager
        self.payroll_engine = None
        
    async def initialize(self):
        """Initialize payroll service and C++ engine"""
        try:
            # Import C++ payroll engine
            import payroll_engine
            self.payroll_engine = payroll_engine
            logger.info("Payroll C++ engine loaded successfully")
        except ImportError as e:
            logger.warning(f"C++ payroll engine not available: {e}")
            logger.info("Falling back to Python implementation")
    
    async def process_payroll(self, payroll_data: Dict[str, Any], db_session: AsyncSession) -> int:
        """Process payroll for specified period and employees"""
        try:
            # Validate payroll data
            await self._validate_payroll_data(payroll_data)
            
            # Create payroll record
            from ..models.payroll import PayrollRun
            
            payroll_run = PayrollRun(
                period_start=datetime.fromisoformat(payroll_data["period_start"]),
                period_end=datetime.fromisoformat(payroll_data["period_end"]),
                pay_date=datetime.fromisoformat(payroll_data.get("pay_date", payroll_data["period_end"])),
                status="processing",
                created_by=payroll_data.get("created_by", "system")
            )
            
            db_session.add(payroll_run)
            await db_session.flush()  # Get the ID
            
            # Get employees to process
            employee_ids = payroll_data.get("employee_ids", [])
            if not employee_ids:
                employee_ids = await self._get_active_employees(db_session)
            
            total_gross_pay = Decimal('0')
            total_net_pay = Decimal('0')
            total_taxes = Decimal('0')
            
            # Process each employee
            for employee_id in employee_ids:
                try:
                    payroll_item = await self._process_employee_payroll(
                        employee_id, payroll_run.id, payroll_data, db_session
                    )
                    
                    # Decrypt amounts for totals
                    gross_pay = Decimal(await decrypt_field(payroll_item.gross_pay))
                    net_pay = Decimal(await decrypt_field(payroll_item.net_pay))
                    taxes = Decimal(await decrypt_field(payroll_item.total_taxes))
                    
                    total_gross_pay += gross_pay
                    total_net_pay += net_pay
                    total_taxes += taxes
                    
                except Exception as e:
                    logger.error(f"Failed to process payroll for employee {employee_id}: {e}")
                    continue
            
            # Update payroll run totals
            payroll_run.total_gross_pay = await encrypt_field(str(total_gross_pay))
            payroll_run.total_net_pay = await encrypt_field(str(total_net_pay))
            payroll_run.total_taxes = await encrypt_field(str(total_taxes))
            payroll_run.status = "completed"
            
            await db_session.commit()
            
            logger.info(f"Payroll processed successfully: {payroll_run.id}")
            return payroll_run.id
            
        except Exception as e:
            await db_session.rollback()
            logger.error(f"Failed to process payroll: {e}")
            raise
    
    async def get_payroll(self, payroll_id: int, db_session: AsyncSession) -> Dict[str, Any]:
        """Retrieve and decrypt payroll information"""
        try:
            from ..models.payroll import PayrollRun, PayrollItem
            
            # Get payroll run
            stmt = select(PayrollRun).where(PayrollRun.id == payroll_id)
            result = await db_session.execute(stmt)
            payroll_run = result.scalar_one_or_none()
            
            if not payroll_run:
                raise ValueError(f"Payroll run {payroll_id} not found")
            
            # Get payroll items
            items_stmt = select(PayrollItem).where(PayrollItem.payroll_run_id == payroll_id)
            items_result = await db_session.execute(items_stmt)
            payroll_items = items_result.scalars().all()
            
            # Decrypt payroll run totals
            decrypted_items = []
            for item in payroll_items:
                item_data = {
                    "employee_id": item.employee_id,
                    "gross_pay": await decrypt_field(item.gross_pay),
                    "net_pay": await decrypt_field(item.net_pay),
                    "total_taxes": await decrypt_field(item.total_taxes),
                    "total_deductions": await decrypt_field(item.total_deductions),
                    "overtime_hours": item.overtime_hours,
                    "overtime_pay": await decrypt_field(item.overtime_pay) if item.overtime_pay else "0"
                }
                decrypted_items.append(item_data)
            
            return {
                "id": payroll_run.id,
                "period_start": payroll_run.period_start.isoformat(),
                "period_end": payroll_run.period_end.isoformat(),
                "pay_date": payroll_run.pay_date.isoformat(),
                "status": payroll_run.status,
                "total_gross_pay": await decrypt_field(payroll_run.total_gross_pay),
                "total_net_pay": await decrypt_field(payroll_run.total_net_pay),
                "total_taxes": await decrypt_field(payroll_run.total_taxes),
                "created_by": payroll_run.created_by,
                "created_at": payroll_run.created_at.isoformat(),
                "items": decrypted_items
            }
            
        except Exception as e:
            logger.error(f"Failed to get payroll {payroll_id}: {e}")
            raise
    
    async def _process_employee_payroll(
        self, 
        employee_id: int, 
        payroll_run_id: int, 
        payroll_data: Dict[str, Any], 
        db_session: AsyncSession
    ):
        """Process payroll for a single employee"""
        from ..models.employee import Employee
        from ..models.payroll import PayrollItem
        
        # Get employee information
        stmt = select(Employee).where(Employee.id == employee_id)
        result = await db_session.execute(stmt)
        employee = result.scalar_one()
        
        # Decrypt salary information
        base_salary = Decimal(await decrypt_field(employee.base_salary))
        
        # Calculate hours worked (this would typically come from time tracking)
        regular_hours = Decimal(payroll_data.get("regular_hours", "40"))
        overtime_hours = Decimal(payroll_data.get("overtime_hours", "0"))
        
        # Use C++ engine for calculations if available
        if self.payroll_engine:
            calculations = await self._calculate_payroll_cpp(
                employee_id, base_salary, regular_hours, overtime_hours
            )
        else:
            calculations = await self._calculate_payroll_python(
                employee_id, base_salary, regular_hours, overtime_hours
            )
        
        # Create payroll item
        payroll_item = PayrollItem(
            payroll_run_id=payroll_run_id,
            employee_id=employee_id,
            gross_pay=await encrypt_field(str(calculations["gross_pay"])),
            net_pay=await encrypt_field(str(calculations["net_pay"])),
            total_taxes=await encrypt_field(str(calculations["total_taxes"])),
            total_deductions=await encrypt_field(str(calculations["total_deductions"])),
            overtime_hours=float(overtime_hours),
            overtime_pay=await encrypt_field(str(calculations["overtime_pay"]))
        )
        
        db_session.add(payroll_item)
        return payroll_item
    
    async def _calculate_payroll_cpp(
        self, 
        employee_id: int, 
        base_salary: Decimal, 
        regular_hours: Decimal, 
        overtime_hours: Decimal
    ) -> Dict[str, Decimal]:
        """Calculate payroll using C++ engine"""
        try:
            # Use C++ engine for high-performance calculations
            result = self.payroll_engine.calculate_employee_payroll(
                employee_id,
                float(base_salary),
                float(regular_hours),
                float(overtime_hours)
            )
            
            return {
                "gross_pay": Decimal(str(result["gross_pay"])),
                "net_pay": Decimal(str(result["net_pay"])),
                "total_taxes": Decimal(str(result["total_taxes"])),
                "total_deductions": Decimal(str(result["total_deductions"])),
                "overtime_pay": Decimal(str(result["overtime_pay"]))
            }
            
        except Exception as e:
            logger.error(f"C++ payroll calculation failed: {e}")
            # Fallback to Python implementation
            return await self._calculate_payroll_python(
                employee_id, base_salary, regular_hours, overtime_hours
            )
    
    async def _calculate_payroll_python(
        self, 
        employee_id: int, 
        base_salary: Decimal, 
        regular_hours: Decimal, 
        overtime_hours: Decimal
    ) -> Dict[str, Decimal]:
        """Calculate payroll using Python implementation"""
        try:
            # Calculate hourly rate (assuming annual salary)
            hourly_rate = base_salary / Decimal('2080')  # 52 weeks * 40 hours
            
            # Calculate regular pay
            regular_pay = hourly_rate * regular_hours
            
            # Calculate overtime pay (1.5x rate)
            overtime_rate = hourly_rate * Decimal('1.5')
            overtime_pay = overtime_rate * overtime_hours
            
            # Calculate gross pay
            gross_pay = regular_pay + overtime_pay
            
            # Calculate taxes (simplified - would use actual tax tables)
            federal_tax_rate = Decimal('0.22')  # 22% federal tax
            state_tax_rate = Decimal('0.05')    # 5% state tax
            social_security_rate = Decimal('0.062')  # 6.2% Social Security
            medicare_rate = Decimal('0.0145')   # 1.45% Medicare
            
            federal_tax = gross_pay * federal_tax_rate
            state_tax = gross_pay * state_tax_rate
            social_security = gross_pay * social_security_rate
            medicare = gross_pay * medicare_rate
            
            total_taxes = federal_tax + state_tax + social_security + medicare
            
            # Calculate other deductions (health insurance, 401k, etc.)
            health_insurance = Decimal('200.00')  # Fixed amount
            retirement_401k = gross_pay * Decimal('0.05')  # 5% contribution
            
            total_deductions = health_insurance + retirement_401k
            
            # Calculate net pay
            net_pay = gross_pay - total_taxes - total_deductions
            
            return {
                "gross_pay": gross_pay,
                "net_pay": net_pay,
                "total_taxes": total_taxes,
                "total_deductions": total_deductions,
                "overtime_pay": overtime_pay
            }
            
        except Exception as e:
            logger.error(f"Python payroll calculation failed: {e}")
            raise
    
    async def _validate_payroll_data(self, payroll_data: Dict[str, Any]):
        """Validate payroll data"""
        required_fields = ["period_start", "period_end"]
        for field in required_fields:
            if field not in payroll_data:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate date formats
        try:
            datetime.fromisoformat(payroll_data["period_start"])
            datetime.fromisoformat(payroll_data["period_end"])
        except ValueError as e:
            raise ValueError(f"Invalid date format: {e}")
    
    async def _get_active_employees(self, db_session: AsyncSession) -> List[int]:
        """Get list of active employee IDs"""
        from ..models.employee import Employee
        
        stmt = select(Employee.id).where(Employee.is_active == True)
        result = await db_session.execute(stmt)
        return [row[0] for row in result.fetchall()]
    
    async def calculate_tax_withholding(
        self, 
        gross_pay: Decimal, 
        employee_data: Dict[str, Any]
    ) -> Dict[str, Decimal]:
        """Calculate tax withholding amounts"""
        try:
            if self.payroll_engine:
                # Use C++ engine for tax calculations
                result = self.payroll_engine.calculate_tax_withholding(
                    float(gross_pay),
                    employee_data.get("filing_status", "single"),
                    employee_data.get("allowances", 0),
                    employee_data.get("state", "CA")
                )
                
                return {
                    "federal_tax": Decimal(str(result["federal_tax"])),
                    "state_tax": Decimal(str(result["state_tax"])),
                    "social_security": Decimal(str(result["social_security"])),
                    "medicare": Decimal(str(result["medicare"]))
                }
            else:
                # Python fallback implementation
                return await self._calculate_tax_withholding_python(gross_pay, employee_data)
                
        except Exception as e:
            logger.error(f"Tax withholding calculation failed: {e}")
            raise
    
    async def _calculate_tax_withholding_python(
        self, 
        gross_pay: Decimal, 
        employee_data: Dict[str, Any]
    ) -> Dict[str, Decimal]:
        """Python implementation of tax withholding calculation"""
        # Simplified tax calculation - in production, use actual tax tables
        filing_status = employee_data.get("filing_status", "single")
        allowances = employee_data.get("allowances", 0)
        
        # Federal tax brackets (simplified)
        if filing_status == "single":
            if gross_pay <= 9950:
                federal_rate = Decimal('0.10')
            elif gross_pay <= 40525:
                federal_rate = Decimal('0.12')
            elif gross_pay <= 86375:
                federal_rate = Decimal('0.22')
            else:
                federal_rate = Decimal('0.24')
        else:  # married
            if gross_pay <= 19900:
                federal_rate = Decimal('0.10')
            elif gross_pay <= 81050:
                federal_rate = Decimal('0.12')
            elif gross_pay <= 172750:
                federal_rate = Decimal('0.22')
            else:
                federal_rate = Decimal('0.24')
        
        # Adjust for allowances
        allowance_amount = Decimal('4300') * allowances  # Per allowance
        taxable_income = max(gross_pay - allowance_amount, Decimal('0'))
        
        federal_tax = taxable_income * federal_rate
        state_tax = gross_pay * Decimal('0.05')  # 5% state tax
        social_security = gross_pay * Decimal('0.062')  # 6.2%
        medicare = gross_pay * Decimal('0.0145')  # 1.45%
        
        return {
            "federal_tax": federal_tax,
            "state_tax": state_tax,
            "social_security": social_security,
            "medicare": medicare
        }
