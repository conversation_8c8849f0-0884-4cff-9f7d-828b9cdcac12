# Enterprise Accounting System Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
APP_NAME="Enterprise Accounting System"
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production
LOG_LEVEL=INFO

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/accounting
DATABASE_ECHO=false

# HashiCorp Vault Configuration
VAULT_URL=http://localhost:8200
VAULT_TOKEN=myroot
VAULT_ROLE_ID=
VAULT_SECRET_ID=
VAULT_TRANSIT_KEY=accounting-key
VAULT_MOUNT_POINT=transit

# Message Broker Configuration
RABBITMQ_URL=amqp://guest:guest@localhost:5672//

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Authentication Configuration
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=accounting
KEYCLOAK_CLIENT_ID=accounting-client
KEYCLOAK_CLIENT_SECRET=

# JWT Configuration
JWT_SECRET_KEY=jwt-secret-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Security Configuration
RATE_LIMIT_PER_MINUTE=100

# File Upload Configuration
UPLOAD_DIRECTORY=./uploads
MAX_FILE_SIZE_MB=10

# Financial Configuration
DEFAULT_CURRENCY=USD
FISCAL_YEAR_START=01-01

# C++ Module Configuration
CPP_BUILD_TYPE=Release
CPP_PARALLEL_JOBS=4

# Development Settings (only for development environment)
RELOAD_ON_CHANGE=true
ENABLE_CORS=true
ENABLE_SWAGGER=true

# Production Settings (only for production environment)
# ENVIRONMENT=production
# DEBUG=false
# DATABASE_ECHO=false
# LOG_LEVEL=WARNING
# ENABLE_CORS=false
# ENABLE_SWAGGER=false

# SSL/TLS Configuration (for production)
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# Monitoring Configuration
# PROMETHEUS_ENABLED=true
# PROMETHEUS_PORT=9090
# GRAFANA_URL=http://localhost:3000

# Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
# BACKUP_STORAGE_PATH=/backups

# Email Configuration (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_USE_TLS=true

# External API Configuration
# FX_RATES_API_KEY=your-fx-api-key
# FX_RATES_PROVIDER=fixer.io

# Performance Tuning
# DB_POOL_SIZE=10
# DB_MAX_OVERFLOW=20
# WORKER_PROCESSES=4
# WORKER_THREADS=2

# Cache Configuration
# REDIS_CACHE_URL=redis://localhost:6379/1
# CACHE_TTL_SECONDS=3600

# Audit Configuration
# AUDIT_ENABLED=true
# AUDIT_LOG_PATH=/var/log/accounting/audit.log
# AUDIT_RETENTION_DAYS=2555  # 7 years for financial records
