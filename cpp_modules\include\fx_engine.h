/**
 * Foreign Exchange Engine Header
 * High-performance currency conversion and FX calculations
 */

#ifndef FX_ENGINE_H
#define FX_ENGINE_H

#include <string>
#include <map>
#include <vector>
#include <memory>
#include <mutex>
#include <chrono>

class FXEngine {
public:
    FXEngine();
    ~FXEngine();
    
    /**
     * Convert amount from one currency to another
     */
    double convert(double amount, const std::string& from_currency, const std::string& to_currency);
    
    /**
     * Get exchange rate between two currencies
     */
    double get_exchange_rate(const std::string& from_currency, const std::string& to_currency);
    
    /**
     * Update exchange rate
     */
    void update_exchange_rate(const std::string& from_currency, const std::string& to_currency, double rate);
    
    /**
     * Get all supported currencies
     */
    std::vector<std::string> get_supported_currencies() const;
    
    /**
     * Calculate cross rates
     */
    double calculate_cross_rate(const std::string& from_currency, const std::string& to_currency, const std::string& base_currency = "USD");
    
    /**
     * Get historical rates (mock implementation)
     */
    std::vector<double> get_historical_rates(const std::string& from_currency, const std::string& to_currency, int days);
    
    /**
     * Calculate volatility
     */
    double calculate_volatility(const std::vector<double>& rates);
    
    /**
     * Refresh rates from external source (mock)
     */
    void refresh_rates();

private:
    std::map<std::pair<std::string, std::string>, double> exchange_rates_;
    std::map<std::pair<std::string, std::string>, std::chrono::system_clock::time_point> rate_timestamps_;
    mutable std::mutex rates_mutex_;
    
    /**
     * Initialize default exchange rates
     */
    void initialize_default_rates();
    
    /**
     * Check if rate is stale
     */
    bool is_rate_stale(const std::string& from_currency, const std::string& to_currency) const;
    
    /**
     * Generate rate key
     */
    std::pair<std::string, std::string> make_rate_key(const std::string& from_currency, const std::string& to_currency) const;
};

#endif // FX_ENGINE_H
