/**
 * Payroll Calculations Header
 * High-performance payroll computation algorithms
 */

#ifndef PAYROLL_CALCULATIONS_H
#define PAYROLL_CALCULATIONS_H

#include <string>
#include <map>
#include <vector>
#include <memory>

class PayrollCalculations {
public:
    PayrollCalculations();
    ~PayrollCalculations();
    
    /**
     * Calculate complete payroll for an employee
     */
    std::map<std::string, double> calculate_full_payroll(
        int employee_id,
        double base_salary,
        double regular_hours,
        double overtime_hours
    );
    
    /**
     * Calculate gross pay
     */
    double calculate_gross_pay(
        double base_salary,
        double regular_hours,
        double overtime_hours,
        double overtime_multiplier = 1.5
    );
    
    /**
     * Calculate net pay after all deductions
     */
    double calculate_net_pay(
        double gross_pay,
        double total_taxes,
        double total_deductions
    );
    
    /**
     * Calculate hourly rate from annual salary
     */
    double calculate_hourly_rate(double annual_salary, double annual_hours = 2080.0);
    
    /**
     * Calculate regular pay
     */
    double calculate_regular_pay(double hourly_rate, double regular_hours);
    
    /**
     * Calculate overtime pay
     */
    double calculate_overtime_pay(
        double hourly_rate,
        double overtime_hours,
        double overtime_multiplier = 1.5
    );
    
    /**
     * Calculate bonus pay
     */
    double calculate_bonus_pay(double bonus_amount, double bonus_tax_rate = 0.22);
    
    /**
     * Calculate commission pay
     */
    double calculate_commission_pay(
        double sales_amount,
        double commission_rate,
        double base_commission = 0.0
    );
    
    /**
     * Calculate vacation pay
     */
    double calculate_vacation_pay(
        double hourly_rate,
        double vacation_hours,
        double vacation_accrual_rate = 0.0769 // 2 weeks per year
    );
    
    /**
     * Calculate sick pay
     */
    double calculate_sick_pay(
        double hourly_rate,
        double sick_hours,
        double sick_accrual_rate = 0.0385 // 1 week per year
    );
    
    /**
     * Calculate holiday pay
     */
    double calculate_holiday_pay(
        double hourly_rate,
        double holiday_hours,
        double holiday_multiplier = 1.0
    );
    
    /**
     * Calculate shift differential
     */
    double calculate_shift_differential(
        double base_pay,
        const std::string& shift_type,
        double differential_rate = 0.10
    );
    
    /**
     * Calculate piece rate pay
     */
    double calculate_piece_rate_pay(
        int pieces_produced,
        double rate_per_piece,
        int minimum_pieces = 0
    );
    
    /**
     * Validate payroll calculations
     */
    bool validate_payroll_calculation(const std::map<std::string, double>& payroll_data);
    
    /**
     * Round monetary amounts to cents
     */
    double round_to_cents(double amount);

private:
    /**
     * Internal calculation helpers
     */
    double apply_rounding_rules(double amount);
    bool is_valid_amount(double amount);
    
    // Configuration
    double default_annual_hours_;
    double minimum_wage_;
    double overtime_threshold_;
};

#endif // PAYROLL_CALCULATIONS_H
