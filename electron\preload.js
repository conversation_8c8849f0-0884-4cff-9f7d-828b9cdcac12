/**
 * Electron Preload Script
 * Exposes safe APIs to the renderer process
 */

const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // App information
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    getPythonPort: () => ipcRenderer.invoke('get-python-port'),
    
    // Backend control
    restartBackend: () => ipcRenderer.invoke('restart-backend'),
    
    // Menu actions
    onMenuAction: (callback) => {
        ipcRenderer.on('menu-action', (event, action, data) => {
            callback(action, data);
        });
    },
    
    // File operations
    selectFile: (options) => ipcRenderer.invoke('select-file', options),
    saveFile: (options) => ipcRenderer.invoke('save-file', options),
    
    // System information
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
    
    // Notifications
    showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body),
    
    // Window controls
    minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
    maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
    closeWindow: () => ipcRenderer.invoke('close-window'),
    
    // Theme management
    setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
    getTheme: () => ipcRenderer.invoke('get-theme'),
    
    // Settings
    getSetting: (key) => ipcRenderer.invoke('get-setting', key),
    setSetting: (key, value) => ipcRenderer.invoke('set-setting', key, value),
    
    // Logging
    log: (level, message) => ipcRenderer.invoke('log', level, message)
});

// Security: Remove Node.js globals from renderer process
delete window.require;
delete window.exports;
delete window.module;

// Add security headers
window.addEventListener('DOMContentLoaded', () => {
    // Prevent new window creation
    window.open = () => {
        console.warn('window.open is disabled for security');
        return null;
    };
    
    // Prevent eval
    window.eval = () => {
        console.warn('eval is disabled for security');
        return null;
    };
});

// Console logging for development
if (process.env.NODE_ENV === 'development') {
    console.log('Preload script loaded successfully');
}
