"""
Celery tasks for background processing
Handles asynchronous operations like report generation, data imports, etc.
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta
from celery import Celery
from celery.schedules import crontab

from .config import get_celery_config, get_settings
from .utils.db import get_async_session
from .utils.encryption import VaultManager

logger = logging.getLogger(__name__)

# Initialize Celery app with default configuration
config = get_celery_config()
celery_app = Celery('accounting')
celery_app.conf.update(config)

# Configure periodic tasks
celery_app.conf.beat_schedule = {
    'generate-daily-reports': {
        'task': 'accounting.tasks.generate_daily_reports',
        'schedule': crontab(hour=6, minute=0),  # 6 AM daily
    },
    'backup-database': {
        'task': 'accounting.tasks.backup_database',
        'schedule': crontab(hour=2, minute=0),  # 2 AM daily
    },
    'refresh-fx-rates': {
        'task': 'accounting.tasks.refresh_fx_rates',
        'schedule': crontab(minute='*/15'),  # Every 15 minutes
    },
    'cleanup-old-logs': {
        'task': 'accounting.tasks.cleanup_old_logs',
        'schedule': crontab(hour=1, minute=0, day_of_week=0),  # Weekly on Sunday
    },
}


def init_celery(settings=None):
    """Initialize Celery application with custom settings"""
    global celery_app

    # Use custom settings if provided, otherwise use default config
    if settings:
        from .config import get_celery_config
        config = get_celery_config()
        celery_app.conf.update(config)

    return celery_app


@celery_app.task(bind=True, max_retries=3)
def generate_daily_reports(self):
    """Generate daily financial reports"""
    try:
        logger.info("Starting daily report generation")
        
        # This would typically generate various reports
        # For now, we'll just log the task
        
        reports_generated = [
            "daily_balance_sheet",
            "daily_income_statement", 
            "daily_cash_flow",
            "daily_trial_balance"
        ]
        
        logger.info(f"Generated reports: {reports_generated}")
        return {"status": "success", "reports": reports_generated}
        
    except Exception as e:
        logger.error(f"Daily report generation failed: {e}")
        raise self.retry(countdown=60 * 5)  # Retry in 5 minutes


@celery_app.task(bind=True, max_retries=3)
def backup_database(self):
    """Create database backup"""
    try:
        logger.info("Starting database backup")

        backup_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
        backup_path = f"/app/backups/{backup_filename}"

        # Create backup (simplified implementation)
        # In production, this would use pg_dump or similar
        logger.info(f"Database backup created: {backup_path}")
        return {"status": "success", "backup_file": backup_path}

    except Exception as e:
        logger.error(f"Database backup failed: {e}")
        raise self.retry(countdown=60 * 10)  # Retry in 10 minutes


@celery_app.task(bind=True, max_retries=3)
def refresh_fx_rates(self):
    """Refresh foreign exchange rates"""
    try:
        logger.info("Refreshing FX rates")
        
        # This would typically fetch from external APIs
        # For now, we'll simulate the process
        
        currencies_updated = [
            "USD/EUR", "USD/GBP", "USD/JPY", "USD/CHF",
            "USD/CAD", "USD/AUD", "USD/NZD"
        ]
        
        logger.info(f"Updated FX rates for: {currencies_updated}")
        return {"status": "success", "currencies": currencies_updated}
        
    except Exception as e:
        logger.error(f"FX rate refresh failed: {e}")
        raise self.retry(countdown=60 * 2)  # Retry in 2 minutes


@celery_app.task
def cleanup_old_logs():
    """Cleanup old log files"""
    try:
        logger.info("Starting log cleanup")

        import glob
        from pathlib import Path

        log_dir = Path("./logs")
        cutoff_date = datetime.now() - timedelta(days=30)

        deleted_files = []

        if log_dir.exists():
            for log_file in glob.glob(str(log_dir / "*.log*")):
                file_path = Path(log_file)
                if file_path.exists() and file_path.stat().st_mtime < cutoff_date.timestamp():
                    file_path.unlink()
                    deleted_files.append(str(file_path))

        logger.info(f"Deleted {len(deleted_files)} old log files")
        return {"status": "success", "deleted_files": len(deleted_files)}

    except Exception as e:
        logger.error(f"Log cleanup failed: {e}")
        return {"status": "error", "error": str(e)}


@celery_app.task(bind=True, max_retries=3)
def process_bulk_journal_entries(self, entries: List[Dict[str, Any]]):
    """Process multiple journal entries in bulk"""
    try:
        logger.info(f"Processing {len(entries)} journal entries in bulk")

        # Simplified implementation for Celery compatibility
        # In production, this would use a sync database session or
        # delegate to an async worker

        processed_entries = []
        failed_entries = []

        for entry in entries:
            try:
                # Simulate processing
                entry_id = f"entry_{len(processed_entries) + 1}"
                processed_entries.append(entry_id)
            except Exception as e:
                logger.error(f"Failed to process entry: {e}")
                failed_entries.append({"entry": entry, "error": str(e)})

        logger.info(f"Processed {len(processed_entries)} entries, {len(failed_entries)} failed")

        return {
            "status": "completed",
            "processed": len(processed_entries),
            "failed": len(failed_entries),
            "processed_ids": processed_entries,
            "failed_entries": failed_entries
        }

    except Exception as e:
        logger.error(f"Bulk journal entry processing failed: {e}")
        raise self.retry(countdown=60 * 5)


@celery_app.task(bind=True, max_retries=3)
def generate_financial_report(self, report_type: str, parameters: Dict[str, Any]):
    """Generate financial reports asynchronously"""
    try:
        logger.info(f"Generating {report_type} report with parameters: {parameters}")

        # Simplified implementation for Celery compatibility
        # In production, this would use a sync database session or
        # delegate to an async worker

        report = {
            "report_type": report_type,
            "parameters": parameters,
            "generated_at": datetime.now().isoformat(),
            "status": "completed"
        }

        if report_type == "balance_sheet":
            report["data"] = {"assets": {}, "liabilities": {}, "equity": {}}
        elif report_type == "income_statement":
            report["data"] = {"revenue": {}, "expenses": {}, "net_income": "0.00"}
        elif report_type == "cash_flow":
            report["data"] = {"operating": {}, "investing": {}, "financing": {}}
        else:
            raise ValueError(f"Unknown report type: {report_type}")

        logger.info(f"Generated {report_type} report successfully")
        return {"status": "success", "report": report}

    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        raise self.retry(countdown=60 * 2)


@celery_app.task(bind=True, max_retries=3)
def import_data_from_file(self, file_path: str, import_type: str):
    """Import data from CSV/Excel files"""
    try:
        logger.info(f"Importing {import_type} data from {file_path}")
        
        import pandas as pd
        
        # Read file based on extension
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_path}")
        
        # Process based on import type
        if import_type == "journal_entries":
            entries = df.to_dict('records')
            # Process entries in bulk
            result = process_bulk_journal_entries.delay(entries)
            return {"status": "processing", "task_id": result.id}
        
        elif import_type == "chart_of_accounts":
            # Import chart of accounts
            imported_accounts = []
            for _, row in df.iterrows():
                # Process account data
                account_data = {
                    "code": row.get("code"),
                    "name": row.get("name"),
                    "account_type": row.get("type"),
                    "parent_id": row.get("parent_id")
                }
                imported_accounts.append(account_data)
            
            logger.info(f"Imported {len(imported_accounts)} accounts")
            return {"status": "success", "imported": len(imported_accounts)}
        
        else:
            raise ValueError(f"Unknown import type: {import_type}")
        
    except Exception as e:
        logger.error(f"Data import failed: {e}")
        raise self.retry(countdown=60 * 2)


@celery_app.task
def send_notification(notification_type: str, recipients: List[str], data: Dict[str, Any]):
    """Send notifications (email, SMS, etc.)"""
    try:
        logger.info(f"Sending {notification_type} notification to {len(recipients)} recipients")

        # This would typically integrate with email/SMS services
        # For now, we'll just log the notification
        logger.info(f"Notification data: {data}")

        logger.info(f"Notification sent: {notification_type} to {recipients}")
        return {"status": "success", "recipients": len(recipients)}

    except Exception as e:
        logger.error(f"Notification sending failed: {e}")
        return {"status": "error", "error": str(e)}


@celery_app.task(bind=True, max_retries=3)
def calculate_payroll(self, payroll_period: str, employee_ids: List[int] = None):
    """Calculate payroll for specified period"""
    try:
        logger.info(f"Calculating payroll for period: {payroll_period}")

        # Simplified implementation for Celery compatibility
        # In production, this would use a sync database session or
        # delegate to an async worker

        # Simulate payroll processing
        payroll_id = f"payroll_{payroll_period}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Log employee count for tracking
        employee_count = len(employee_ids) if employee_ids else 0
        logger.info(f"Processing payroll for {employee_count} employees")

        logger.info(f"Payroll calculated successfully: {payroll_id}")
        return {"status": "success", "payroll_id": payroll_id}

    except Exception as e:
        logger.error(f"Payroll calculation failed: {e}")
        raise self.retry(countdown=60 * 5)


# Task monitoring and utilities
@celery_app.task
def health_check():
    """Health check task for monitoring"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "worker": "accounting-worker"
    }
