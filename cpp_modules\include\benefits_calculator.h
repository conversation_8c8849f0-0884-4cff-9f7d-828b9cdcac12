/**
 * Benefits Calculator Header
 * High-performance employee benefits calculations
 */

#ifndef BENEFITS_CALCULATOR_H
#define BENEFITS_CALCULATOR_H

#include <string>
#include <map>
#include <vector>
#include <memory>

class BenefitsCalculator {
public:
    BenefitsCalculator();
    ~BenefitsCalculator();
    
    /**
     * Calculate all benefits deductions
     */
    std::map<std::string, double> calculate_deductions(
        int employee_id,
        double gross_pay,
        const std::map<std::string, double>& benefit_elections
    );
    
    /**
     * Calculate health insurance premium
     */
    double calculate_health_insurance_premium(
        const std::string& plan_type,
        const std::string& coverage_level,
        double employee_contribution_percentage = 0.8
    );
    
    /**
     * Calculate dental insurance premium
     */
    double calculate_dental_insurance_premium(
        const std::string& plan_type,
        const std::string& coverage_level,
        double employee_contribution_percentage = 0.8
    );
    
    /**
     * Calculate vision insurance premium
     */
    double calculate_vision_insurance_premium(
        const std::string& coverage_level,
        double employee_contribution_percentage = 0.8
    );
    
    /**
     * Calculate 401(k) contribution
     */
    double calculate_401k_contribution(
        double gross_pay,
        double contribution_percentage,
        double ytd_contributions = 0.0,
        double annual_limit = 22500.0,
        int age = 30
    );
    
    /**
     * Calculate 401(k) employer match
     */
    double calculate_401k_employer_match(
        double employee_contribution,
        double gross_pay,
        double match_percentage = 3.0,
        double match_limit = 6.0
    );
    
    /**
     * Calculate HSA contribution
     */
    double calculate_hsa_contribution(
        double contribution_amount,
        const std::string& coverage_level,
        double ytd_contributions = 0.0,
        int age = 30
    );
    
    /**
     * Calculate FSA contribution
     */
    double calculate_fsa_contribution(
        double contribution_amount,
        const std::string& fsa_type,
        double ytd_contributions = 0.0
    );
    
    /**
     * Calculate life insurance premium
     */
    double calculate_life_insurance_premium(
        double coverage_amount,
        int age,
        const std::string& coverage_type = "term",
        double rate_per_1000 = 0.50
    );
    
    /**
     * Calculate disability insurance premium
     */
    double calculate_disability_insurance_premium(
        double gross_pay,
        const std::string& coverage_type,
        double coverage_percentage = 0.6
    );
    
    /**
     * Calculate commuter benefits
     */
    double calculate_commuter_benefits(
        double transit_amount,
        double parking_amount,
        double monthly_transit_limit = 300.0,
        double monthly_parking_limit = 300.0
    );
    
    /**
     * Calculate dependent care FSA
     */
    double calculate_dependent_care_fsa(
        double contribution_amount,
        const std::string& filing_status,
        double ytd_contributions = 0.0
    );
    
    /**
     * Calculate tuition reimbursement
     */
    double calculate_tuition_reimbursement(
        double tuition_amount,
        double reimbursement_percentage = 0.8,
        double annual_limit = 5250.0,
        double ytd_reimbursements = 0.0
    );
    
    /**
     * Calculate wellness program incentive
     */
    double calculate_wellness_incentive(
        const std::map<std::string, bool>& wellness_activities,
        const std::map<std::string, double>& incentive_amounts
    );
    
    /**
     * Calculate total benefits value
     */
    double calculate_total_benefits_value(
        const std::map<std::string, double>& benefit_values
    );
    
    /**
     * Calculate benefits as percentage of salary
     */
    double calculate_benefits_percentage(
        double total_benefits_value,
        double annual_salary
    );
    
    /**
     * Validate benefit elections
     */
    bool validate_benefit_elections(
        const std::map<std::string, double>& elections,
        double gross_pay,
        const std::map<std::string, double>& limits
    );

private:
    /**
     * Internal calculation helpers
     */
    double get_insurance_rate(
        const std::string& insurance_type,
        const std::string& plan_type,
        const std::string& coverage_level
    );
    
    double get_age_factor(int age, const std::string& benefit_type);
    double apply_contribution_limits(double amount, double limit, double ytd_amount);
    
    /**
     * Benefits configuration
     */
    void initialize_insurance_rates();
    void initialize_contribution_limits();
    void initialize_age_factors();
    
    // Benefits data
    std::map<std::string, std::map<std::string, std::map<std::string, double>>> insurance_rates_;
    std::map<std::string, double> contribution_limits_;
    std::map<std::string, std::vector<std::pair<int, double>>> age_factors_;
    
    // Configuration
    int current_year_;
    double default_employee_contribution_;
    bool enable_catch_up_contributions_;
};

#endif // BENEFITS_CALCULATOR_H
