/**
 * Ledger Engine C++ Module
 * High-performance financial calculations using pybind11
 */

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include <vector>
#include <map>
#include <string>
#include <cmath>
#include <algorithm>
#include <memory>
#include <mutex>
#include <unordered_map>

#include "include/ledger_calculations.h"
#include "include/fx_engine.h"
#include "include/balance_calculator.h"

namespace py = pybind11;

/**
 * High-performance FX allocation calculation
 */
double calculate_fx_allocation(double amount, const std::string& from_currency, const std::string& to_currency) {
    FXEngine fx_engine;
    return fx_engine.convert(amount, from_currency, to_currency);
}

/**
 * Account balance update with thread safety
 */
double update_account_balance(int account_id, double amount, bool is_debit) {
    static std::mutex balance_mutex;
    static std::unordered_map<int, double> account_balances;
    
    std::lock_guard<std::mutex> lock(balance_mutex);
    
    double current_balance = account_balances[account_id];
    
    if (is_debit) {
        current_balance += amount;
    } else {
        current_balance -= amount;
    }
    
    account_balances[account_id] = current_balance;
    return current_balance;
}

/**
 * High-performance account balance calculation
 */
double calculate_account_balance(int account_id, double as_of_timestamp) {
    BalanceCalculator calculator;
    return calculator.calculate_balance(account_id, as_of_timestamp);
}

/**
 * Batch journal entry processing
 */
std::vector<double> process_journal_entries(const std::vector<std::map<std::string, py::object>>& entries) {
    std::vector<double> results;
    LedgerCalculations calc;
    
    for (const auto& entry : entries) {
        double total = 0.0;
        
        // Extract lines from entry
        if (entry.find("lines") != entry.end()) {
            py::list lines = entry.at("lines").cast<py::list>();
            
            for (auto line : lines) {
                py::dict line_dict = line.cast<py::dict>();
                double amount = line_dict["amount"].cast<double>();
                std::string type = line_dict["type"].cast<std::string>();
                
                if (type == "debit") {
                    total += amount;
                } else {
                    total -= amount;
                }
            }
        }
        
        results.push_back(total);
    }
    
    return results;
}

/**
 * Financial ratio calculations
 */
std::map<std::string, double> calculate_financial_ratios(
    const std::map<std::string, double>& balance_sheet,
    const std::map<std::string, double>& income_statement
) {
    std::map<std::string, double> ratios;
    
    // Current Ratio
    if (balance_sheet.find("current_liabilities") != balance_sheet.end() && 
        balance_sheet.at("current_liabilities") != 0.0) {
        ratios["current_ratio"] = balance_sheet.at("current_assets") / balance_sheet.at("current_liabilities");
    }
    
    // Quick Ratio
    if (balance_sheet.find("current_liabilities") != balance_sheet.end() && 
        balance_sheet.at("current_liabilities") != 0.0) {
        double quick_assets = balance_sheet.at("current_assets") - balance_sheet.at("inventory");
        ratios["quick_ratio"] = quick_assets / balance_sheet.at("current_liabilities");
    }
    
    // Debt to Equity Ratio
    if (balance_sheet.find("total_equity") != balance_sheet.end() && 
        balance_sheet.at("total_equity") != 0.0) {
        ratios["debt_to_equity"] = balance_sheet.at("total_debt") / balance_sheet.at("total_equity");
    }
    
    // Return on Assets (ROA)
    if (balance_sheet.find("total_assets") != balance_sheet.end() && 
        balance_sheet.at("total_assets") != 0.0) {
        ratios["return_on_assets"] = income_statement.at("net_income") / balance_sheet.at("total_assets");
    }
    
    // Return on Equity (ROE)
    if (balance_sheet.find("total_equity") != balance_sheet.end() && 
        balance_sheet.at("total_equity") != 0.0) {
        ratios["return_on_equity"] = income_statement.at("net_income") / balance_sheet.at("total_equity");
    }
    
    // Gross Profit Margin
    if (income_statement.find("revenue") != income_statement.end() && 
        income_statement.at("revenue") != 0.0) {
        ratios["gross_profit_margin"] = income_statement.at("gross_profit") / income_statement.at("revenue");
    }
    
    // Net Profit Margin
    if (income_statement.find("revenue") != income_statement.end() && 
        income_statement.at("revenue") != 0.0) {
        ratios["net_profit_margin"] = income_statement.at("net_income") / income_statement.at("revenue");
    }
    
    return ratios;
}

/**
 * Compound interest calculation for financial projections
 */
double calculate_compound_interest(double principal, double rate, int periods, int compounds_per_period) {
    return principal * std::pow(1.0 + (rate / compounds_per_period), compounds_per_period * periods);
}

/**
 * Present value calculation
 */
double calculate_present_value(double future_value, double rate, int periods) {
    return future_value / std::pow(1.0 + rate, periods);
}

/**
 * Net Present Value calculation for investment analysis
 */
double calculate_npv(const std::vector<double>& cash_flows, double discount_rate) {
    double npv = 0.0;
    
    for (size_t i = 0; i < cash_flows.size(); ++i) {
        npv += cash_flows[i] / std::pow(1.0 + discount_rate, i);
    }
    
    return npv;
}

/**
 * Internal Rate of Return calculation
 */
double calculate_irr(const std::vector<double>& cash_flows, double initial_guess = 0.1, int max_iterations = 100) {
    double rate = initial_guess;
    double tolerance = 1e-6;
    
    for (int i = 0; i < max_iterations; ++i) {
        double npv = 0.0;
        double npv_derivative = 0.0;
        
        for (size_t j = 0; j < cash_flows.size(); ++j) {
            double factor = std::pow(1.0 + rate, j);
            npv += cash_flows[j] / factor;
            npv_derivative -= j * cash_flows[j] / (factor * (1.0 + rate));
        }
        
        if (std::abs(npv) < tolerance) {
            return rate;
        }
        
        if (std::abs(npv_derivative) < tolerance) {
            break; // Avoid division by zero
        }
        
        rate = rate - npv / npv_derivative;
    }
    
    return rate;
}

/**
 * Depreciation calculation (straight-line method)
 */
double calculate_straight_line_depreciation(double cost, double salvage_value, int useful_life) {
    return (cost - salvage_value) / useful_life;
}

/**
 * Depreciation calculation (double-declining balance method)
 */
double calculate_double_declining_depreciation(double cost, double accumulated_depreciation, int useful_life) {
    double book_value = cost - accumulated_depreciation;
    double depreciation_rate = 2.0 / useful_life;
    return book_value * depreciation_rate;
}

// Python module definition
PYBIND11_MODULE(ledger_engine, m) {
    m.doc() = "High-performance ledger engine for Enterprise Accounting System";
    
    // Basic calculations
    m.def("calculate_fx_allocation", &calculate_fx_allocation, 
          "Calculate foreign exchange allocation",
          py::arg("amount"), py::arg("from_currency"), py::arg("to_currency"));
    
    m.def("update_account_balance", &update_account_balance,
          "Update account balance with thread safety",
          py::arg("account_id"), py::arg("amount"), py::arg("is_debit"));
    
    m.def("calculate_account_balance", &calculate_account_balance,
          "Calculate account balance as of specific date",
          py::arg("account_id"), py::arg("as_of_timestamp") = 0.0);
    
    // Batch processing
    m.def("process_journal_entries", &process_journal_entries,
          "Process multiple journal entries in batch",
          py::arg("entries"));
    
    // Financial analysis
    m.def("calculate_financial_ratios", &calculate_financial_ratios,
          "Calculate financial ratios from balance sheet and income statement",
          py::arg("balance_sheet"), py::arg("income_statement"));
    
    // Investment calculations
    m.def("calculate_compound_interest", &calculate_compound_interest,
          "Calculate compound interest",
          py::arg("principal"), py::arg("rate"), py::arg("periods"), py::arg("compounds_per_period") = 1);
    
    m.def("calculate_present_value", &calculate_present_value,
          "Calculate present value",
          py::arg("future_value"), py::arg("rate"), py::arg("periods"));
    
    m.def("calculate_npv", &calculate_npv,
          "Calculate Net Present Value",
          py::arg("cash_flows"), py::arg("discount_rate"));
    
    m.def("calculate_irr", &calculate_irr,
          "Calculate Internal Rate of Return",
          py::arg("cash_flows"), py::arg("initial_guess") = 0.1, py::arg("max_iterations") = 100);
    
    // Depreciation calculations
    m.def("calculate_straight_line_depreciation", &calculate_straight_line_depreciation,
          "Calculate straight-line depreciation",
          py::arg("cost"), py::arg("salvage_value"), py::arg("useful_life"));
    
    m.def("calculate_double_declining_depreciation", &calculate_double_declining_depreciation,
          "Calculate double-declining balance depreciation",
          py::arg("cost"), py::arg("accumulated_depreciation"), py::arg("useful_life"));
}
