# Core Python dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
click==8.1.7

# Database
sqlalchemy==2.0.36
asyncpg==0.29.0
psycopg2-binary==2.9.9
alembic==1.13.0
aiosqlite==0.20.0

# Security and Encryption
hvac==2.0.0
PyNaCl==1.5.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
cryptography==41.0.8

# Authentication
python-keycloak==3.7.0
httpx==0.25.2

# Task Queue and Message Broker
celery==5.3.4
redis==5.0.1
kombu==5.3.4

# C++ Integration
pybind11==2.11.1
cmake==3.27.9

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Utilities
python-dotenv==1.0.0
python-multipart==0.0.6
aiofiles==23.2.1
Jinja2==3.1.2

# Data Processing
pandas==2.1.3
numpy==1.25.2
openpyxl==3.1.2

# Monitoring and Logging
structlog==23.2.0
prometheus-client==0.19.0

# Optional: For production deployment
gunicorn==21.2.0
supervisor==4.2.5
