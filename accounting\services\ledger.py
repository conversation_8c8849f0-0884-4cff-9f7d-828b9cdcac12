"""
Ledger service with C++ engine integration
Handles journal entries, account management, and financial transactions
"""

import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from ..utils.encryption import VaultManager, encrypt_field, decrypt_field
from ..models.account import Account
from ..models.journal import JournalEntry, JournalLine
from ..models.transaction import Transaction

logger = logging.getLogger(__name__)


class LedgerService:
    """Ledger service with high-performance C++ engine integration"""
    
    def __init__(self, vault_manager: VaultManager):
        self.vault_manager = vault_manager
        self.ledger_engine = None
        
    async def initialize(self):
        """Initialize ledger service and C++ engine"""
        try:
            # Import C++ ledger engine
            import ledger_engine
            self.ledger_engine = ledger_engine
            logger.info("Ledger C++ engine loaded successfully")
        except ImportError as e:
            logger.warning(f"C++ ledger engine not available: {e}")
            logger.info("Falling back to Python implementation")
    
    async def create_journal_entry(self, entry_data: Dict[str, Any], db_session: AsyncSession) -> int:
        """Create a new journal entry with encrypted sensitive data"""
        try:
            # Validate journal entry
            await self._validate_journal_entry(entry_data)
            
            # Create journal entry
            journal_entry = JournalEntry(
                description=entry_data["description"],
                reference=entry_data.get("reference"),
                entry_date=datetime.fromisoformat(entry_data["entry_date"]),
                created_by=entry_data.get("created_by", "system")
            )
            
            db_session.add(journal_entry)
            await db_session.flush()  # Get the ID
            
            total_debits = Decimal('0')
            total_credits = Decimal('0')
            
            # Process journal lines
            for line_data in entry_data["lines"]:
                # Encrypt sensitive amounts
                encrypted_amount = await encrypt_field(str(line_data["amount"]))
                
                # Use C++ engine for FX calculations if available
                if self.ledger_engine and line_data.get("currency") != entry_data.get("base_currency", "USD"):
                    fx_amount = self.ledger_engine.calculate_fx_allocation(
                        float(line_data["amount"]),
                        line_data.get("currency", "USD"),
                        entry_data.get("base_currency", "USD")
                    )
                    encrypted_fx_amount = await encrypt_field(str(fx_amount))
                else:
                    encrypted_fx_amount = encrypted_amount
                
                journal_line = JournalLine(
                    journal_entry_id=journal_entry.id,
                    account_id=line_data["account_id"],
                    description=line_data.get("description", ""),
                    debit_amount=encrypted_amount if line_data["type"] == "debit" else None,
                    credit_amount=encrypted_amount if line_data["type"] == "credit" else None,
                    currency=line_data.get("currency", "USD"),
                    fx_rate=line_data.get("fx_rate", 1.0),
                    base_amount=encrypted_fx_amount
                )
                
                db_session.add(journal_line)
                
                # Track totals for validation
                amount = Decimal(str(line_data["amount"]))
                if line_data["type"] == "debit":
                    total_debits += amount
                else:
                    total_credits += amount
            
            # Validate balanced entry
            if total_debits != total_credits:
                raise ValueError(f"Journal entry not balanced: debits={total_debits}, credits={total_credits}")
            
            # Update account balances using C++ engine if available
            if self.ledger_engine:
                await self._update_account_balances_cpp(entry_data["lines"], db_session)
            else:
                await self._update_account_balances_python(entry_data["lines"], db_session)
            
            await db_session.commit()
            
            logger.info(f"Journal entry created: {journal_entry.id}")
            return journal_entry.id
            
        except Exception as e:
            await db_session.rollback()
            logger.error(f"Failed to create journal entry: {e}")
            raise
    
    async def get_journal_entry(self, entry_id: int, db_session: AsyncSession) -> Dict[str, Any]:
        """Retrieve and decrypt journal entry"""
        try:
            # Get journal entry with lines
            stmt = select(JournalEntry).where(JournalEntry.id == entry_id)
            result = await db_session.execute(stmt)
            journal_entry = result.scalar_one_or_none()
            
            if not journal_entry:
                raise ValueError(f"Journal entry {entry_id} not found")
            
            # Get journal lines
            lines_stmt = select(JournalLine).where(JournalLine.journal_entry_id == entry_id)
            lines_result = await db_session.execute(lines_stmt)
            journal_lines = lines_result.scalars().all()
            
            # Decrypt amounts
            decrypted_lines = []
            for line in journal_lines:
                line_data = {
                    "id": line.id,
                    "account_id": line.account_id,
                    "description": line.description,
                    "currency": line.currency,
                    "fx_rate": line.fx_rate
                }
                
                if line.debit_amount:
                    line_data["type"] = "debit"
                    line_data["amount"] = await decrypt_field(line.debit_amount)
                else:
                    line_data["type"] = "credit"
                    line_data["amount"] = await decrypt_field(line.credit_amount)
                
                if line.base_amount:
                    line_data["base_amount"] = await decrypt_field(line.base_amount)
                
                decrypted_lines.append(line_data)
            
            return {
                "id": journal_entry.id,
                "description": journal_entry.description,
                "reference": journal_entry.reference,
                "entry_date": journal_entry.entry_date.isoformat(),
                "created_by": journal_entry.created_by,
                "created_at": journal_entry.created_at.isoformat(),
                "lines": decrypted_lines
            }
            
        except Exception as e:
            logger.error(f"Failed to get journal entry {entry_id}: {e}")
            raise
    
    async def get_account_balance(self, account_id: int, db_session: AsyncSession, as_of_date: datetime = None) -> Dict[str, Any]:
        """Get account balance with optional as-of date"""
        try:
            # Get account
            stmt = select(Account).where(Account.id == account_id)
            result = await db_session.execute(stmt)
            account = result.scalar_one_or_none()
            
            if not account:
                raise ValueError(f"Account {account_id} not found")
            
            # Use C++ engine for balance calculation if available
            if self.ledger_engine:
                balance = await self._calculate_balance_cpp(account_id, db_session, as_of_date)
            else:
                balance = await self._calculate_balance_python(account_id, db_session, as_of_date)
            
            return {
                "account_id": account_id,
                "account_code": account.code,
                "account_name": account.name,
                "account_type": account.account_type,
                "balance": balance,
                "as_of_date": as_of_date.isoformat() if as_of_date else datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get account balance for {account_id}: {e}")
            raise
    
    async def _validate_journal_entry(self, entry_data: Dict[str, Any]):
        """Validate journal entry data"""
        required_fields = ["description", "entry_date", "lines"]
        for field in required_fields:
            if field not in entry_data:
                raise ValueError(f"Missing required field: {field}")
        
        if not entry_data["lines"]:
            raise ValueError("Journal entry must have at least one line")
        
        if len(entry_data["lines"]) < 2:
            raise ValueError("Journal entry must have at least two lines")
        
        for line in entry_data["lines"]:
            required_line_fields = ["account_id", "amount", "type"]
            for field in required_line_fields:
                if field not in line:
                    raise ValueError(f"Missing required line field: {field}")
            
            if line["type"] not in ["debit", "credit"]:
                raise ValueError(f"Invalid line type: {line['type']}")
            
            if Decimal(str(line["amount"])) <= 0:
                raise ValueError("Line amount must be positive")
    
    async def _update_account_balances_cpp(self, lines: List[Dict[str, Any]], db_session: AsyncSession):
        """Update account balances using C++ engine"""
        try:
            for line in lines:
                account_id = line["account_id"]
                amount = float(line["amount"])
                is_debit = line["type"] == "debit"
                
                # Use C++ engine for balance calculation
                new_balance = self.ledger_engine.update_account_balance(
                    account_id, amount, is_debit
                )
                
                # Update account record
                stmt = select(Account).where(Account.id == account_id)
                result = await db_session.execute(stmt)
                account = result.scalar_one()
                
                # Encrypt new balance
                encrypted_balance = await encrypt_field(str(new_balance))
                account.current_balance = encrypted_balance
                
        except Exception as e:
            logger.error(f"Failed to update account balances with C++ engine: {e}")
            raise
    
    async def _update_account_balances_python(self, lines: List[Dict[str, Any]], db_session: AsyncSession):
        """Update account balances using Python implementation"""
        try:
            for line in lines:
                account_id = line["account_id"]
                amount = Decimal(str(line["amount"]))
                
                # Get current account
                stmt = select(Account).where(Account.id == account_id)
                result = await db_session.execute(stmt)
                account = result.scalar_one()
                
                # Decrypt current balance
                current_balance = Decimal('0')
                if account.current_balance:
                    current_balance = Decimal(await decrypt_field(account.current_balance))
                
                # Calculate new balance based on account type and transaction type
                if account.account_type in ['asset', 'expense']:
                    # Debit increases, credit decreases
                    if line["type"] == "debit":
                        new_balance = current_balance + amount
                    else:
                        new_balance = current_balance - amount
                else:
                    # Liability, equity, revenue: credit increases, debit decreases
                    if line["type"] == "credit":
                        new_balance = current_balance + amount
                    else:
                        new_balance = current_balance - amount
                
                # Encrypt and update balance
                encrypted_balance = await encrypt_field(str(new_balance))
                account.current_balance = encrypted_balance
                
        except Exception as e:
            logger.error(f"Failed to update account balances with Python: {e}")
            raise
    
    async def _calculate_balance_cpp(self, account_id: int, db_session: AsyncSession, as_of_date: datetime = None) -> str:
        """Calculate account balance using C++ engine"""
        try:
            # Use C++ engine for high-performance balance calculation
            balance = self.ledger_engine.calculate_account_balance(
                account_id,
                as_of_date.timestamp() if as_of_date else None
            )
            return str(balance)
        except Exception as e:
            logger.error(f"C++ balance calculation failed: {e}")
            # Fallback to Python implementation
            return await self._calculate_balance_python(account_id, db_session, as_of_date)
    
    async def _calculate_balance_python(self, account_id: int, db_session: AsyncSession, as_of_date: datetime = None) -> str:
        """Calculate account balance using Python implementation"""
        try:
            # Build query for journal lines
            stmt = select(JournalLine).where(JournalLine.account_id == account_id)
            
            if as_of_date:
                stmt = stmt.join(JournalEntry).where(JournalEntry.entry_date <= as_of_date)
            
            result = await db_session.execute(stmt)
            lines = result.scalars().all()
            
            balance = Decimal('0')
            for line in lines:
                if line.debit_amount:
                    amount = Decimal(await decrypt_field(line.debit_amount))
                    balance += amount
                elif line.credit_amount:
                    amount = Decimal(await decrypt_field(line.credit_amount))
                    balance -= amount
            
            return str(balance)
            
        except Exception as e:
            logger.error(f"Python balance calculation failed: {e}")
            raise
