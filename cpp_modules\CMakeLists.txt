cmake_minimum_required(VERSION 3.12)
project(accounting_engines)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(pybind11 REQUIRED)

# Include directories
include_directories(include)

# Compiler-specific options
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
endif()

# Ledger Engine Module
pybind11_add_module(ledger_engine 
    ledger_engine.cpp
    src/ledger_calculations.cpp
    src/fx_engine.cpp
    src/balance_calculator.cpp
)

# Payroll Engine Module
pybind11_add_module(payroll_engine 
    payroll_engine.cpp
    src/payroll_calculations.cpp
    src/tax_calculator.cpp
    src/benefits_calculator.cpp
)

# Set properties for modules
set_target_properties(ledger_engine PROPERTIES
    CXX_VISIBILITY_PRESET "hidden"
    VISIBILITY_INLINES_HIDDEN ON
)

set_target_properties(payroll_engine PROPERTIES
    CXX_VISIBILITY_PRESET "hidden"
    VISIBILITY_INLINES_HIDDEN ON
)

# Link libraries if needed
if(WIN32)
    # Windows-specific libraries
    target_link_libraries(ledger_engine PRIVATE)
    target_link_libraries(payroll_engine PRIVATE)
elseif(UNIX)
    # Unix-specific libraries
    target_link_libraries(ledger_engine PRIVATE pthread)
    target_link_libraries(payroll_engine PRIVATE pthread)
endif()

# Optimization flags
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    if(MSVC)
        target_compile_options(ledger_engine PRIVATE /O2 /Ob2)
        target_compile_options(payroll_engine PRIVATE /O2 /Ob2)
    else()
        target_compile_options(ledger_engine PRIVATE -O3 -march=native)
        target_compile_options(payroll_engine PRIVATE -O3 -march=native)
    endif()
endif()

# Debug information
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    if(MSVC)
        target_compile_options(ledger_engine PRIVATE /Zi)
        target_compile_options(payroll_engine PRIVATE /Zi)
    else()
        target_compile_options(ledger_engine PRIVATE -g)
        target_compile_options(payroll_engine PRIVATE -g)
    endif()
endif()

# Install targets
install(TARGETS ledger_engine payroll_engine
    LIBRARY DESTINATION .
    RUNTIME DESTINATION .
)
