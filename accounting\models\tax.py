"""
Tax models for tax calculations
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Float
from sqlalchemy.sql import func

from ..utils.db import Base


class TaxRate(Base):
    """Tax rate model"""
    
    __tablename__ = "tax_rates"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Tax information
    tax_type = Column(String(50), nullable=False)  # federal, state, local, sales, etc.
    jurisdiction = Column(String(100), nullable=False)  # US, CA, NY, etc.
    rate = Column(Float, nullable=False)
    
    # Effective dates
    effective_date = Column(DateTime(timezone=True), nullable=False)
    expiration_date = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<TaxRate(type='{self.tax_type}', jurisdiction='{self.jurisdiction}', rate={self.rate})>"


class TaxCalculation(Base):
    """Tax calculation results"""
    
    __tablename__ = "tax_calculations"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Reference information
    entity_type = Column(String(50), nullable=False)  # payroll, transaction, etc.
    entity_id = Column(Integer, nullable=False)
    
    # Tax details
    tax_type = Column(String(50), nullable=False)
    jurisdiction = Column(String(100), nullable=False)
    
    # Amounts (encrypted)
    taxable_amount = Column(Text, nullable=False)
    tax_rate = Column(Float, nullable=False)
    tax_amount = Column(Text, nullable=False)
    
    # Metadata
    calculation_date = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<TaxCalculation(type='{self.tax_type}', entity_type='{self.entity_type}', entity_id={self.entity_id})>"
