"""
Pytest configuration and fixtures for Enterprise Accounting System tests
"""

import pytest
import asyncio
import os
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import after path setup
from accounting.config import Settings, get_settings
from accounting.utils.encryption import <PERSON><PERSON><PERSON><PERSON><PERSON>, LibsodiumManager
from accounting.utils.db import Base, get_async_session
from accounting.services.auth import AuthService
from accounting.services.ledger import LedgerService
from accounting.services.payroll import PayrollService
from accounting.services.report import ReportService


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()


@pytest.fixture
def test_settings():
    """Test settings configuration"""
    return Settings(
        app_name="Test Accounting System",
        debug=True,
        database_url="sqlite+aiosqlite:///:memory:",
        vault_url="http://localhost:8200",
        vault_token="test-token",
        rabbitmq_url="memory://",
        celery_broker_url="memory://",
        jwt_secret_key="test-jwt-secret",
        secret_key="test-secret-key"
    )


@pytest.fixture
def mock_vault_manager():
    """Mock Vault manager for testing"""
    vault_manager = Mock(spec=VaultManager)
    vault_manager.initialize = AsyncMock()
    vault_manager.encrypt = AsyncMock(return_value="encrypted_data")
    vault_manager.decrypt = AsyncMock(return_value="decrypted_data")
    vault_manager._authenticated = True
    return vault_manager


@pytest.fixture
def libsodium_manager():
    """Real libsodium manager for testing"""
    return LibsodiumManager()


@pytest.fixture
def auth_service(mock_vault_manager):
    """Auth service with mocked dependencies"""
    return AuthService(mock_vault_manager)


@pytest.fixture
def ledger_service(mock_vault_manager):
    """Ledger service with mocked dependencies"""
    service = LedgerService(mock_vault_manager)
    # Mock C++ engine
    service.ledger_engine = Mock()
    service.ledger_engine.calculate_fx_allocation.return_value = 850.0
    service.ledger_engine.update_account_balance.return_value = 5000.0
    service.ledger_engine.calculate_account_balance.return_value = 1000.0
    return service


@pytest.fixture
def payroll_service(mock_vault_manager):
    """Payroll service with mocked dependencies"""
    service = PayrollService(mock_vault_manager)
    # Mock C++ engine
    service.payroll_engine = Mock()
    service.payroll_engine.calculate_employee_payroll.return_value = {
        "gross_pay": 5000.0,
        "net_pay": 3500.0,
        "total_taxes": 1200.0,
        "total_deductions": 300.0,
        "overtime_pay": 500.0
    }
    return service


@pytest.fixture
def report_service(mock_vault_manager):
    """Report service with mocked dependencies"""
    return ReportService(mock_vault_manager)


@pytest.fixture
def mock_db_session():
    """Mock database session"""
    session = AsyncMock()
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.flush = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.fixture
def sample_journal_entry():
    """Sample journal entry data for testing"""
    return {
        "description": "Test journal entry",
        "entry_date": "2023-12-01T00:00:00",
        "reference": "TEST-001",
        "created_by": "test_user",
        "lines": [
            {
                "account_id": 1,
                "amount": "1000.00",
                "type": "debit",
                "description": "Test debit line"
            },
            {
                "account_id": 2,
                "amount": "1000.00",
                "type": "credit",
                "description": "Test credit line"
            }
        ]
    }


@pytest.fixture
def sample_payroll_data():
    """Sample payroll data for testing"""
    return {
        "period_start": "2023-12-01T00:00:00",
        "period_end": "2023-12-15T00:00:00",
        "pay_date": "2023-12-20T00:00:00",
        "created_by": "test_user",
        "employee_ids": [1, 2, 3],
        "regular_hours": "80",
        "overtime_hours": "5"
    }


@pytest.fixture
def sample_employee_data():
    """Sample employee data for testing"""
    return {
        "employee_id": "EMP001",
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "hire_date": "2023-01-01",
        "base_salary": "50000",
        "filing_status": "single",
        "allowances": 1
    }


# Mock external dependencies
@pytest.fixture(autouse=True)
def mock_external_deps():
    """Mock external dependencies that might not be available in test environment"""
    with patch('hvac.Client') as mock_hvac:
        mock_client = Mock()
        mock_client.is_authenticated.return_value = True
        mock_client.secrets.transit.encrypt_data.return_value = {
            'data': {'ciphertext': 'vault:v1:encrypted_data'}
        }
        mock_client.secrets.transit.decrypt_data.return_value = {
            'data': {'plaintext': 'dGVzdCBkYXRh'}  # base64 encoded "test data"
        }
        mock_hvac.return_value = mock_client
        
        with patch('accounting.utils.encryption.encrypt_field') as mock_encrypt:
            mock_encrypt.return_value = "encrypted_field_data"
            
            with patch('accounting.utils.encryption.decrypt_field') as mock_decrypt:
                mock_decrypt.return_value = "1000.00"
                
                yield


# Test database setup
@pytest.fixture
async def test_db_engine():
    """Create test database engine"""
    from sqlalchemy.ext.asyncio import create_async_engine
    from sqlalchemy.pool import StaticPool
    
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()


# Utility functions for tests
def assert_decimal_equal(actual, expected, places=2):
    """Assert that two decimal values are equal within specified decimal places"""
    from decimal import Decimal
    if isinstance(actual, str):
        actual = Decimal(actual)
    if isinstance(expected, str):
        expected = Decimal(expected)
    
    assert abs(actual - expected) < Decimal(10) ** -places


def create_mock_account(account_id=1, code="1000", name="Test Account", account_type="asset"):
    """Create a mock account object"""
    account = Mock()
    account.id = account_id
    account.code = code
    account.name = name
    account.account_type = account_type
    account.is_current = True
    account.is_cash = False
    account.is_operating = True
    account.current_balance = "encrypted_balance"
    return account


def create_mock_employee(employee_id=1, emp_code="EMP001", base_salary="50000"):
    """Create a mock employee object"""
    employee = Mock()
    employee.id = employee_id
    employee.employee_id = emp_code
    employee.first_name = "John"
    employee.last_name = "Doe"
    employee.base_salary = "encrypted_salary"
    employee.filing_status = "single"
    employee.allowances = 1
    employee.is_active = True
    return employee
