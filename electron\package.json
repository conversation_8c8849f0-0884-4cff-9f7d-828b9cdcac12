{"name": "enterprise-accounting", "version": "1.0.0", "description": "Enterprise Accounting & Financial Management System", "main": "main.js", "author": "Enterprise Accounting Team", "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder --win", "build-all": "electron-builder --win --mac --linux", "pack": "electron-builder --dir", "dist": "npm run build", "postinstall": "electron-builder install-app-deps"}, "homepage": "./", "dependencies": {"electron-log": "^4.4.8", "electron-updater": "^5.3.0", "electron-store": "^8.1.0"}, "devDependencies": {"electron": "^26.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.enterprise.accounting", "productName": "Enterprise Accounting", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "renderer.js", "index.html", "assets/**/*", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Enterprise Accounting"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.finance"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "extraResources": [{"from": "../", "to": "python-backend", "filter": ["main.py", "accounting/**/*", "cpp_modules/build/**/*", "requirements.txt"]}]}, "engines": {"node": ">=16.0.0"}}