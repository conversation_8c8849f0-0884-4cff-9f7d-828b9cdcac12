/**
 * Payroll Calculations Implementation
 * High-performance payroll computation algorithms
 */

#include "../include/payroll_calculations.h"
#include "../include/tax_calculator.h"
#include "../include/benefits_calculator.h"
#include <cmath>
#include <algorithm>
#include <stdexcept>

PayrollCalculations::PayrollCalculations() 
    : default_annual_hours_(2080.0)
    , minimum_wage_(7.25)
    , overtime_threshold_(40.0) {
}

PayrollCalculations::~PayrollCalculations() = default;

std::map<std::string, double> PayrollCalculations::calculate_full_payroll(
    int employee_id,
    double base_salary,
    double regular_hours,
    double overtime_hours
) {
    std::map<std::string, double> result;
    
    try {
        // Calculate gross pay
        double gross_pay = calculate_gross_pay(base_salary, regular_hours, overtime_hours);
        
        // Calculate taxes
        TaxCalculator tax_calc;
        auto tax_result = tax_calc.calculate_withholding(gross_pay, "single", 1, "CA");
        
        double total_taxes = tax_result["federal_tax"] + tax_result["state_tax"] + 
                           tax_result["social_security"] + tax_result["medicare"];
        
        // Calculate benefits
        BenefitsCalculator benefits_calc;
        std::map<std::string, double> benefit_elections = {
            {"health_insurance", 1.0},
            {"dental_insurance", 1.0},
            {"retirement_401k", 5.0} // 5% contribution
        };
        auto benefits_result = benefits_calc.calculate_deductions(employee_id, gross_pay, benefit_elections);
        
        double total_deductions = benefits_result["health_insurance"] + 
                                benefits_result["dental_insurance"] + 
                                benefits_result["retirement_401k"];
        
        // Calculate net pay
        double net_pay = calculate_net_pay(gross_pay, total_taxes, total_deductions);
        
        // Build result
        result["gross_pay"] = round_to_cents(gross_pay);
        result["net_pay"] = round_to_cents(net_pay);
        result["total_taxes"] = round_to_cents(total_taxes);
        result["total_deductions"] = round_to_cents(total_deductions);
        result["federal_tax"] = round_to_cents(tax_result["federal_tax"]);
        result["state_tax"] = round_to_cents(tax_result["state_tax"]);
        result["social_security"] = round_to_cents(tax_result["social_security"]);
        result["medicare"] = round_to_cents(tax_result["medicare"]);
        result["overtime_pay"] = round_to_cents(calculate_overtime_pay(
            calculate_hourly_rate(base_salary), overtime_hours
        ));
        
        // Validate results
        if (!validate_payroll_calculation(result)) {
            throw std::runtime_error("Payroll calculation validation failed");
        }
        
    } catch (const std::exception& e) {
        // Return error result
        result["error"] = 1.0;
        result["gross_pay"] = 0.0;
        result["net_pay"] = 0.0;
        result["total_taxes"] = 0.0;
        result["total_deductions"] = 0.0;
    }
    
    return result;
}

double PayrollCalculations::calculate_gross_pay(
    double base_salary,
    double regular_hours,
    double overtime_hours,
    double overtime_multiplier
) {
    double hourly_rate = calculate_hourly_rate(base_salary);
    double regular_pay = calculate_regular_pay(hourly_rate, regular_hours);
    double overtime_pay = calculate_overtime_pay(hourly_rate, overtime_hours, overtime_multiplier);
    
    return regular_pay + overtime_pay;
}

double PayrollCalculations::calculate_net_pay(
    double gross_pay,
    double total_taxes,
    double total_deductions
) {
    return std::max(0.0, gross_pay - total_taxes - total_deductions);
}

double PayrollCalculations::calculate_hourly_rate(double annual_salary, double annual_hours) {
    if (annual_hours <= 0) {
        annual_hours = default_annual_hours_;
    }
    return annual_salary / annual_hours;
}

double PayrollCalculations::calculate_regular_pay(double hourly_rate, double regular_hours) {
    // Ensure minimum wage compliance
    double effective_rate = std::max(hourly_rate, minimum_wage_);
    return effective_rate * regular_hours;
}

double PayrollCalculations::calculate_overtime_pay(
    double hourly_rate,
    double overtime_hours,
    double overtime_multiplier
) {
    if (overtime_hours <= 0) {
        return 0.0;
    }
    
    double effective_rate = std::max(hourly_rate, minimum_wage_);
    return effective_rate * overtime_hours * overtime_multiplier;
}

double PayrollCalculations::calculate_bonus_pay(double bonus_amount, double bonus_tax_rate) {
    // Bonus pay is typically subject to supplemental tax rate
    return bonus_amount * (1.0 - bonus_tax_rate);
}

double PayrollCalculations::calculate_commission_pay(
    double sales_amount,
    double commission_rate,
    double base_commission
) {
    return base_commission + (sales_amount * commission_rate);
}

double PayrollCalculations::calculate_vacation_pay(
    double hourly_rate,
    double vacation_hours,
    double vacation_accrual_rate
) {
    // Calculate vacation pay based on accrued hours
    double accrued_hours = vacation_hours * vacation_accrual_rate;
    return hourly_rate * std::min(vacation_hours, accrued_hours);
}

double PayrollCalculations::calculate_sick_pay(
    double hourly_rate,
    double sick_hours,
    double sick_accrual_rate
) {
    // Calculate sick pay based on accrued hours
    double accrued_hours = sick_hours * sick_accrual_rate;
    return hourly_rate * std::min(sick_hours, accrued_hours);
}

double PayrollCalculations::calculate_holiday_pay(
    double hourly_rate,
    double holiday_hours,
    double holiday_multiplier
) {
    return hourly_rate * holiday_hours * holiday_multiplier;
}

double PayrollCalculations::calculate_shift_differential(
    double base_pay,
    const std::string& shift_type,
    double differential_rate
) {
    if (shift_type == "night" || shift_type == "weekend") {
        return base_pay * differential_rate;
    }
    return 0.0;
}

double PayrollCalculations::calculate_piece_rate_pay(
    int pieces_produced,
    double rate_per_piece,
    int minimum_pieces
) {
    if (pieces_produced < minimum_pieces) {
        return 0.0;
    }
    return pieces_produced * rate_per_piece;
}

bool PayrollCalculations::validate_payroll_calculation(const std::map<std::string, double>& payroll_data) {
    // Basic validation checks
    auto gross_it = payroll_data.find("gross_pay");
    auto net_it = payroll_data.find("net_pay");
    auto taxes_it = payroll_data.find("total_taxes");
    auto deductions_it = payroll_data.find("total_deductions");
    
    if (gross_it == payroll_data.end() || net_it == payroll_data.end() ||
        taxes_it == payroll_data.end() || deductions_it == payroll_data.end()) {
        return false;
    }
    
    double gross_pay = gross_it->second;
    double net_pay = net_it->second;
    double total_taxes = taxes_it->second;
    double total_deductions = deductions_it->second;
    
    // Validate amounts are non-negative
    if (gross_pay < 0 || net_pay < 0 || total_taxes < 0 || total_deductions < 0) {
        return false;
    }
    
    // Validate net pay calculation
    double expected_net = gross_pay - total_taxes - total_deductions;
    if (std::abs(net_pay - expected_net) > 0.01) { // Allow 1 cent rounding difference
        return false;
    }
    
    // Validate reasonable tax rates (taxes shouldn't exceed 50% of gross)
    if (total_taxes > gross_pay * 0.5) {
        return false;
    }
    
    return true;
}

double PayrollCalculations::round_to_cents(double amount) {
    return std::round(amount * 100.0) / 100.0;
}

double PayrollCalculations::apply_rounding_rules(double amount) {
    // Apply standard payroll rounding rules
    return round_to_cents(amount);
}

bool PayrollCalculations::is_valid_amount(double amount) {
    return std::isfinite(amount) && amount >= 0.0;
}
