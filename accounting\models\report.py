"""
Report models for report templates and instances
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.sql import func

from ..utils.db import Base


class ReportTemplate(Base):
    """Report template model"""
    
    __tablename__ = "report_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Template information
    name = Column(String(255), nullable=False)
    description = Column(Text)
    report_type = Column(String(50), nullable=False)  # balance_sheet, income_statement, etc.
    
    # Template configuration
    template_config = Column(JSON)  # JSON configuration for the report
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100))
    
    def __repr__(self):
        return f"<ReportTemplate(name='{self.name}', type='{self.report_type}')>"


class ReportInstance(Base):
    """Generated report instance"""
    
    __tablename__ = "report_instances"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Report information
    template_id = Column(Integer)
    report_name = Column(String(255), nullable=False)
    report_type = Column(String(50), nullable=False)
    
    # Report parameters
    parameters = Column(JSON)  # JSON parameters used to generate the report
    
    # Report data (encrypted)
    report_data = Column(Text)  # Encrypted report data
    
    # Status
    status = Column(String(20), default="generated")  # generated, archived, deleted
    
    # Metadata
    generated_at = Column(DateTime(timezone=True), server_default=func.now())
    generated_by = Column(String(100))
    
    def __repr__(self):
        return f"<ReportInstance(name='{self.report_name}', type='{self.report_type}', generated_at='{self.generated_at}')>"
